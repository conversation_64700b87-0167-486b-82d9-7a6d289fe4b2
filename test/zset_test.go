package test

import (
	"context"
	"github.com/redis/go-redis/v9"
	"strconv"
	"testing"
)

func BenchmarkZAdd(b *testing.B) {
	client := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "redis123zxc",
		DB:       0,
	})
	ctx := context.Background()
	b.Cleanup(func() {
		client.Del(ctx, "test_zset")
	})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		client.ZAdd(ctx, "test_zset", redis.Z{Score: float64(i), Member: "member" + strconv.Itoa(i)})
		//client.ZRemRangeByRank(ctx, "test_zset", 0, 1000)
		//client.ZRangeWithScores(ctx, "test_zset", 0, 0)
	}
}

func BenchmarkZAddParallel(b *testing.B) {
	client := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "redis123zxc",
		DB:       0,
	})
	ctx := context.Background()
	b.Cleanup(func() {
		client.Del(ctx, "test_zset")
	})

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			client.ZAdd(ctx, "test_zset", redis.Z{
				Score:  float64(i),
				Member: "member" + strconv.Itoa(i),
			})
			client.ZRemRangeByRank(ctx, "test_zset", 0, 1000)
			client.ZRangeWithScores(ctx, "test_zset", 0, 0)
			i++
		}
	})
}

const (
	leaderboardKey = "bench_leaderboard"
	topN           = 1000
)

// helper：初始化并预填 1000 条数据
func setupLeaderboard(ctx context.Context, client *redis.Client) error {
	// 先清空
	if err := client.Del(ctx, leaderboardKey).Err(); err != nil {
		return err
	}
	// 批量写入
	members := make([]redis.Z, topN)
	for i := 0; i < topN; i++ {
		members[i] = redis.Z{
			Score:  float64(i),
			Member: "user" + strconv.Itoa(i),
		}
	}
	return client.ZAdd(ctx, leaderboardKey, members...).Err()
}

func BenchmarkZRangeTop1000(b *testing.B) {
	client := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "redis123zxc",
		DB:       0,
	})
	ctx := context.Background()

	// 每次结束后清理
	b.Cleanup(func() {
		client.Del(ctx, leaderboardKey)
	})
	// 先准备数据
	if err := setupLeaderboard(ctx, client); err != nil {
		b.Fatalf("setup failed: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 取出分数最高的 1000 条
		_, err := client.ZRevRangeWithScores(ctx, leaderboardKey, 0, topN-1).Result()
		if err != nil {
			b.Fatalf("ZRevRange error: %v", err)
		}
	}
}

func BenchmarkZRangeTop1000Parallel(b *testing.B) {
	// 并发版本里，每个 goroutine 都用独立的 client 连接，更贴近真实并发场景
	ctx := context.Background()

	b.Cleanup(func() {
		// 最后再清理
		client := redis.NewClient(&redis.Options{
			Addr:     "localhost:6379",
			Password: "redis123zxc",
			DB:       0,
		})
		client.Del(ctx, leaderboardKey)
	})

	// 先用一个 client 写入数据
	{
		client := redis.NewClient(&redis.Options{
			Addr:     "localhost:6379",
			Password: "redis123zxc",
			DB:       0,
		})
		if err := setupLeaderboard(ctx, client); err != nil {
			b.Fatalf("setup failed: %v", err)
		}
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		client := redis.NewClient(&redis.Options{
			Addr:     "localhost:6379",
			Password: "redis123zxc",
			DB:       0,
		})
		for pb.Next() {
			_, err := client.ZRevRangeWithScores(ctx, leaderboardKey, 0, topN-1).Result()
			if err != nil {
				b.Fatalf("ZRevRangeParallel error: %v", err)
			}
		}
	})
}
