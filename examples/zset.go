package main

func EncodeCompositeScore(score int64, timestamp int64, uid int64) float64 {
	// 注意：时间戳越小越好，我们反转它（如 9999999999 - timestamp）
	// UID 越小越好，也反转（如 9999999999 - uid）
	const (
		timeMax = int64(9999999999)
		uidMax  = int64(9999999999)
	)

	timeWeight := timeMax - timestamp
	uidWeight := uidMax - uid

	// 保证不同部分不会重叠（拼接逻辑）
	composite := score*1e20 + timeWeight*1e10 + uidWeight
	return float64(composite)
}
