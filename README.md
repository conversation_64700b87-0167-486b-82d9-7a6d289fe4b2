# 开罗游戏服务端

## 项目介绍

开罗游戏服务端是一个基于Go语言开发的分布式游戏服务器架构，采用微服务设计理念，为开罗系列游戏提供稳定、高效、可扩展的后端服务支持。该架构包含多个独立的服务组件，通过协同工作，实现游戏的登录认证、实时通信、游戏逻辑处理等功能。

### 系统架构

系统采用分层架构设计，主要包含以下几个核心服务：

- **登录服务（Login Server）**：处理玩家登录认证，生成和管理令牌，分配网关和游戏服以及其他的服务信息
- **网关服务（Gate Server）**：管理客户端连接，转发消息
- **游戏服务（Game Server）**：处理游戏核心逻辑，本地进程存储玩家数据
- **公共服务（Public Server）**：作为跨服的公共服务，提供各服务间共享的功能和数据

各服务之间通过gRPC进行通信，使用Protobuf作为数据序列化格式，保证了通信的高效性和跨平台特性。

### 技术栈

- **开发环境**：Windows 11
- **语言**：Go 1.24+
- **Web框架**：Gin
- **数据库**：MySQL + Redis
- **服务发现**：Etcd
- **通信协议**：HTTP、WebSocket、gRPC
- **序列化**：Protocol Buffers
- **认证**：JWT
- **配置管理**：Viper
- **日志**：Zap

## 目录结构与模块说明

### 测试文件路径规则
路径：在相应的模块的test目录下，例如public模块的测试文件，应该是在services/public/test

#### example路径规则
路径：在相应的模块的examples目录下，例如public模块的测试文件，应该是在services/public/examples

```
├── bin/                # 编译后的二进制文件和配置文件
│   ├── config/         # 配置文件目录
│   │   ├── develop/    # 开发环境配置
│   │   ├── game/       # 游戏配置文件
│   │   └── testing/    # 测试环境配置
│   └── logs/           # 日志文件
│
├── cmd/                # 各服务的入口文件
│   ├── game/           # 游戏服务入口
│   ├── gate/           # 网关服务入口
│   ├── login/          # 登录服务入口
│   ├── public/         # 公共服务入口
│   └── servo/          # 初始化服务
│
├── examples/           # 示例代码
│
├── internal/           # 内部共享包
│   ├── bootstrap/      # 服务启动初始化
│   ├── common/         # 通用工具
│   │   ├── encrypt/    # 加密相关工具
│   │   ├── pool/       # 对象池
│   │   ├── protobuf/   # Protobuf工具
│   │   └── response/   # HTTP响应工具
│   │
│   ├── config/         # 配置管理
│   ├── consts/         # 常量定义
│   ├── db/             # 数据库连接
│   │   ├── mongo/      # MongoDB连接
│   │   ├── mysql/      # MySQL连接
│   │   └── redis_client/ # Redis连接
│   │
│   ├── discovery/      # 服务发现与注册（基于Etcd）
│   │   └── stats/      # 服务状态统计
│   │
│   ├── event/          # 事件系统
│   ├── game_config/    # 游戏配置管理
│   ├── grpc/           # gRPC客户端工具
│   ├── handler/        # 消息处理器
│   ├── I/              # 接口定义
│   ├── jwt/            # JWT认证
│   ├── logger/         # 日志系统
│   ├── middleware/     # HTTP中间件
│   ├── models/         # 数据模型
│   ├── sdk_platform/   # 第三方SDK集成
│   │   └── leiting/    # 雷霆SDK集成
│   │
│   └── serv/           # 服务管理
│
├── proto/              # Protobuf定义文件
│
└── services/           # 服务实现
    │
    ├── game/           # 游戏服务
    │   └── internal/    # 游戏服务内部实现
    │       ├── grpc/     # gRPC服务实现
    │       ├── handlers/ # 消息处理器
    │       ├── logic/    # 业务逻辑
    │       ├── module/   # 模块实现
    │       │   ├── player/       # 玩家模块
    │       │   │   ├── assets/      # 玩家资产
    │       │   │   ├── init_player/ # 玩家初始化
    │       │   │   └── user/        # 玩家用户信息
    │       │   └── player_manage/ # 玩家管理
    │       └── utils/    # 工具函数
    │
    ├── gate/           # 网关服务
    │   ├── external/    # 外部接口
    │   │   └── grpc/     # gRPC客户端
    │   │
    │   └── internal/    # 内部实现
    │       ├── auth/     # 认证
    │       ├── connection/ # 连接管理
    │       ├── controller/ # 控制器
    │       └── handlers/ # 消息处理器
    │
    ├── login/          # 登录服务
    │   ├── external/    # 外部接口
    │   └── internal/    # 内部实现
    │       ├── controller/ # 控制器
    │       ├── logic/    # 业务逻辑
    │       └── model/    # 数据模型
    │
    ├── public/         # 公共服务
    │   ├── client/      # 客户端工具
    │   └── internal/    # 内部实现
    │       ├── grpc/     # gRPC服务实现
    │       ├── handlers/ # 消息处理器
    │       │   └── system/ # 系统相关处理
    │       ├── logic/    # 业务逻辑
    │       └── module/   # 功能模块
    │
    ├── pb/             # 生成的Protobuf代码
    │   ├── export_exe/ # Protobuf导出工具
    │   └── msg/       # 消息定义
    │
    └── servo/          # 初始化服务
        ├── external/    # 外部接口
        └── internal/    # 内部实现
            └── controller/ # 控制器
```

## 核心服务与模块说明

### 1、登录服务（Login）

- **功能说明**：
  - 负责玩家登录验证账号，分配网关服务器地址
  - 缓存用户token和网关服地址信息
  - 支持多种渠道登录认证（如雷霆SDK等）

- **核心模块**：
  - `external`：外部HTTP API接口，供客户端调用
  - `internal/controller`：控制器，处理HTTP请求
  - `internal/logic`：业务逻辑，实现登录验证、Token生成等功能
  - `internal/model`：数据模型，定义用户数据结构

- **Token机制**：
  - 生成短期的AccessToken（JWT）和长期的RefreshToken
  - AccessToken用于日常接口访问认证
  - RefreshToken用于在AccessToken过期后获取新的AccessToken

### 2、网关服务（Gate）

- **功能说明**：
  - 与客户端通过WebSocket建立长连接，实现实时通信
  - 验证客户端Token的有效性
  - 转发客户端请求到相应的游戏服务

- **核心模块**：
  - `external/grpc`：外部gRPC客户端，用于连接游戏服务
  - `internal/auth`：认证模块，验证客户端Token
  - `internal/connection`：连接管理，管理WebSocket连接
  - `internal/handlers`：消息处理器，处理客户端消息

- **消息路由**：
  - 消息ID 10000-29999 转发至游戏服务
  - 消息ID 30000-40000 转发至公共服务

- **连接管理**：
  - 客户端5分钟无活动自动断开连接
  - 消息队列满时自动断开连接
  - 支持心跳检测机制

### 3、游戏服务（Game）

- **功能说明**：
  - 实现游戏核心逻辑
  - 提供gRPC接口供网关服务调用
  - 处理玩家游戏数据的读写
  - 管理游戏状态和规则

- **核心模块**：
  - `internal/grpc`：实现gRPC服务，接收网关服务转发的请求
  - `internal/handlers`：消息处理器，处理各类游戏消息
  - `internal/logic`：业务逻辑，实现游戏功能
  - `internal/module/player`：玩家模块，管理玩家数据和状态
  - `internal/module/player_manage`：玩家管理，管理所有在线玩家

- **玩家模块详解**：
  - `player`：玩家核心模块，定义玩家对象及其行为
  - `player/assets`：玩家资产管理，处理货币、道具等
  - `player/init_player`：玩家初始化逻辑，包含各种回调函数注册
  - `player/user`：玩家用户信息管理
  - `player_manage`：玩家管理器，管理所有在线玩家

- **玩家生命周期**：
  - 初始化：玩家连接时创建玩家对象
  - 登录：玩家登录游戏，加载数据，调用`OnLoginSuccess`回调
  - 离线：玩家断开连接，进入离线状态，调用`OnDisconnect`回调
  - 销毁：玩家离线30分钟后，调用`OnDestroy`回调，销毁玩家对象并释放内存

- **断线重连机制**：
  - 玩家断线后进入离线状态，启动定时器
  - 如果玩家在指定时间内（30分钟）重新连接，取消定时器
  - 如果超时未重连，销毁玩家对象并释放内存

### 4、服务发现与注册（Discovery）

- **功能说明**：
  - 基于Etcd实现的服务发现与注册机制
  - 支持服务健康检查和状态监控
  - 提供服务负载均衡策略

- **核心模块**：
  - `discovery`：服务发现与注册核心实现
  - `discovery/stats`：服务状态统计，收集服务性能指标

### 5、公共服务（Public）

- **功能说明**：
  - 作为跨服的公共服务，提供各服务间共享的功能和数据
  - 通过gRPC接口与其他服务进行通信
  - 实现服务间的数据同步和共享功能

- **核心模块**：
  - `internal/grpc`：实现gRPC服务，接收其他服务的请求
  - `internal/handlers`：消息处理器，处理各类跨服消息
  - `internal/logic`：业务逻辑，实现跨服功能
  - `internal/module`：功能模块，实现具体的跨服功能
  - `client`：客户端工具，简化其他服务调用公共服务的过程

- **跨服功能示例**：
  - 全服公告管理
  - 跨服排行榜
  - 全局配置同步
  - 系统状态监控

### 6、数据存储（Storage）

- **功能说明**：
  - 使用MySQL存储持久化数据
  - 使用Redis缓存高频访问数据
  - 支持数据库连接池管理

- **核心模块**：
  - `db/mysql`：MySQL数据库连接与操作
  - `db/redis_client`：Redis缓存连接与操作
  - `models`：数据模型定义

## 配置说明

系统配置文件位于`bin/config/`目录下，主要包括：

- `config.yaml`：全局配置，包含数据库、Redis、JWT等配置
- `login.yaml`：登录服务配置
- `gate.yaml`：网关服务配置
- `server.yaml`：游戏服务配置
- `public.yaml`：公共服务配置

开发环境的配置文件位于`bin/config/develop/`目录下。

## 构建与运行

### 编译服务

```bash
# 编译登录服务
cd bin
.\build.bat login

# 编译网关服务
.\build.bat gate

# 编译游戏服务
.\build.bat game

# 编译公共服务
.\build.bat public
```

### 生成Protobuf代码

```bash
gen_proto.bat
```

### 运行服务

```bash
# 运行登录服务
cd bin
login-game.exe

# 运行网关服务
gate-game.exe

# 运行游戏服务
game-game.exe

# 运行公共服务
public-game.exe
```

## 客户端示例

在`examples`目录下提供了多个客户端示例，展示如何与服务端进行交互：

- `login_client.go`：演示如何使用Protobuf与登录服务交互
- `jwt_example.go`：演示JWT令牌的生成和验证
- `logger_example.go`: 演示如何使用Zap日志系统

### 服务器地址
### 开发环境
  - 初始化服务：`192.168.31.52:8080`