syntax = "proto2";
option go_package = "kairo_paradise_server/services/pb;pb";
package pb;
import "code.proto";
import "gameconfig.proto";

// 排行榜模块协议
// 30100 - 30149

// 排行榜数据
message RankEntryData {
  required uint64 player_id = 1;      // 玩家ID
  optional string player_name = 2;    // 玩家名称
  optional int32 level = 3;           // 玩家等级
  optional int32 prosperity = 4;      // 繁荣度
  optional int32  ranking = 5;        // 排行
  optional int32  icon = 6;            // 玩家头像
}

// 更新排行榜数据请求
// 30100
// USED
message G2SUpdateRank {
  required ERankType rank_type = 1;   // 排行榜类型
  required RankEntryData entry = 2;   // 排行榜条目数据
}

// 更新排行榜数据响应
// 30101
// USED
message S2GUpdateRank {
  required response_code code = 1;    // 响应码
  optional string message = 2;        // 错误信息
  optional int32 rank = 3;            // 更新后的排名，如果未上榜则为-1
}

// 获取排行榜数据请求
// 30102
// USED
message C2SGetRankList {
  required ERankType rank_type = 1;   // 排行榜类型
  required int32 start = 2;           // 起始排名（从1开始）
  required int32 count = 3;           // 获取数量
}

// 获取排行榜数据响应
// 30103
// USED
message S2CGetRankList {
  required response_code code = 1;    // 响应码
  optional string message = 2;        // 错误信息
  repeated RankEntryData entries = 3; // 排行榜数据
  optional int32 total_count = 4;     // 排行榜总条目数
}

// 获取玩家排名请求
// 30104
// USED
message G2SGetPlayerRank {
  required ERankType rank_type = 1;   // 排行榜类型
  required uint64 player_id = 2;      // 玩家ID
}

// 获取玩家排名响应
// 30105
// USED
message S2GGetPlayerRank {
  required response_code code = 1;    // 响应码
  optional string message = 2;        // 错误信息
  optional int32 rank = 3;            // 玩家排名，如果未上榜则为-1
  optional RankEntryData entry = 4;   // 玩家排行榜数据
}

// 清除排行榜请求（仅管理员使用）
// 30106
// USED
message G2SClearRank {
  required ERankType rank_type = 1;   // 排行榜类型
}

