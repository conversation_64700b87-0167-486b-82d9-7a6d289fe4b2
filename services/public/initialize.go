package public

import (
	"context"
	"google.golang.org/grpc"
	"kairo_paradise_server/internal/config"
	"kairo_paradise_server/internal/discovery"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	grpc2 "kairo_paradise_server/services/public/internal/grpc"
	"kairo_paradise_server/services/public/internal/handlers"
	"kairo_paradise_server/services/public/internal/module/rank"
)

var StatsCollector *discovery.StatsCollector

// Initialize initializes the public service
func Initialize(ctx context.Context) {
	// Register the public service with the discovery system
	_, _ = discovery.RegisterService(discovery.ServiceTypePublic, config.ServerConf.ServerConfig.Addr, map[string]string{
		"version": "1.0.0",
		"region":  "cn",
	})

	logger.Info("Initializing public service")

	// Initialize rank manager
	rankManager := rank.GetRankManager()
	if err := rankManager.Initialize(); err != nil {
		logger.Errorf("Failed to initialize rank manager: %v", err)
	}

	// Register handlers
	handlers.Register()
}

// RegisterGRPCServer registers the gRPC server for the public service
func RegisterGRPCServer(server *grpc.Server) {
	pb.RegisterGRpcServiceServer(server, &grpc2.Server{})
}
