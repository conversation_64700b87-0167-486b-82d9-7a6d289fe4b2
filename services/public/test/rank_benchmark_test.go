package test

import (
	"fmt"
	"math/rand"
	"runtime"
	"sync"
	"testing"
	"time"

	"kairo_paradise_server/services/public/internal/module/rank"
)

// BenchmarkConfig 基准测试配置
type BenchmarkConfig struct {
	RankType      int32
	MinScoreLimit int32
	MaxRankLimit  int32
	PlayerCount   int
	IsRealtime    bool
}

// createTestStore 创建测试用的内存存储
func createTestStore(config *BenchmarkConfig) (*rank.MemoryStore, *rank.Config) {
	store := rank.NewMemoryStore()
	rankConfig := &rank.Config{
		RankType:      config.RankType,
		MinScoreLimit: config.MinScoreLimit,
		MaxRankLimit:  config.MaxRankLimit,
		IsRealtime:    config.IsRealtime,
		MaxQueryLimit: 1000,
		ShowRankLimit: 100,
	}
	rankConfig.InitializeDynamicLimit()
	store.InitializeRankTypes([]int32{config.RankType})
	return store, rankConfig
}

// generateTestEntry 生成测试条目
func generateTestEntry(playerID uint64, score int32) *rank.Entry {
	return rank.NewEntry(
		playerID,
		fmt.Sprintf("Player%d", playerID),
		int32(10+rand.Intn(90)), // 等级10-99
		int32(1+rand.Intn(10)),  // 头像1-10
		score,
	)
}

// populateStore 填充存储器
func populateStore(store *rank.MemoryStore, config *rank.Config, playerCount int) {
	for i := 0; i < playerCount; i++ {
		playerID := uint64(i + 1)
		score := int32(1000 + rand.Intn(9000)) // 分数1000-9999
		entry := generateTestEntry(playerID, score)
		store.UpdateEntry(config.RankType, entry, config)
	}
}

// =============================================================================
// 基本操作基准测试
// =============================================================================

// BenchmarkUpdateEntry 测试更新条目的性能
func BenchmarkUpdateEntry(b *testing.B) {
	benchConfigs := []BenchmarkConfig{
		{RankType: 1, MinScoreLimit: 100, MaxRankLimit: 1000, PlayerCount: 0, IsRealtime: true},
		{RankType: 1, MinScoreLimit: 100, MaxRankLimit: 5000, PlayerCount: 0, IsRealtime: true},
		{RankType: 1, MinScoreLimit: 100, MaxRankLimit: 10000, PlayerCount: 0, IsRealtime: true},
	}

	for _, config := range benchConfigs {
		b.Run(fmt.Sprintf("MaxRank_%d", config.MaxRankLimit), func(b *testing.B) {
			store, rankConfig := createTestStore(&config)

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				playerID := uint64(i + 1)
				score := int32(1000 + rand.Intn(9000))
				entry := generateTestEntry(playerID, score)

				_, _, err := store.UpdateEntry(rankConfig.RankType, entry, rankConfig)
				if err != nil {
					b.Fatalf("Failed to update entry: %v", err)
				}
			}
		})
	}
}

// BenchmarkUpdateEntryWithExistingData 测试在已有数据基础上更新条目的性能
func BenchmarkUpdateEntryWithExistingData(b *testing.B) {
	benchConfigs := []BenchmarkConfig{
		{RankType: 1, MinScoreLimit: 100, MaxRankLimit: 1000, PlayerCount: 500, IsRealtime: true},
		{RankType: 1, MinScoreLimit: 100, MaxRankLimit: 5000, PlayerCount: 2500, IsRealtime: true},
		{RankType: 1, MinScoreLimit: 100, MaxRankLimit: 10000, PlayerCount: 5000, IsRealtime: true},
	}

	for _, config := range benchConfigs {
		b.Run(fmt.Sprintf("MaxRank_%d_Existing_%d", config.MaxRankLimit, config.PlayerCount), func(b *testing.B) {
			store, rankConfig := createTestStore(&config)

			// 预填充数据
			populateStore(store, rankConfig, config.PlayerCount)

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				// 50%概率更新已存在的玩家，50%概率添加新玩家
				var playerID uint64
				if rand.Float32() < 0.5 && config.PlayerCount > 0 {
					playerID = uint64(rand.Intn(config.PlayerCount) + 1)
				} else {
					playerID = uint64(config.PlayerCount + i + 1)
				}

				score := int32(1000 + rand.Intn(9000))
				entry := generateTestEntry(playerID, score)

				_, _, err := store.UpdateEntry(rankConfig.RankType, entry, rankConfig)
				if err != nil {
					b.Fatalf("Failed to update entry: %v", err)
				}
			}
		})
	}
}

// BenchmarkGetRankList 测试获取排行榜列表的性能
func BenchmarkGetRankList(b *testing.B) {
	config := BenchmarkConfig{
		RankType:      1,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
		PlayerCount:   5000,
		IsRealtime:    true,
	}

	store, rankConfig := createTestStore(&config)
	populateStore(store, rankConfig, config.PlayerCount)

	testCases := []struct {
		name  string
		start int32
		count int32
	}{
		{"Top10", 1, 10},
		{"Top50", 1, 50},
		{"Top100", 1, 100},
		{"Middle100", 1000, 100},
		{"Bottom100", 4900, 100},
	}

	for _, tc := range testCases {
		b.Run(tc.name, func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, _, err := store.GetRankList(rankConfig.RankType, tc.start, tc.count)
				if err != nil {
					b.Fatalf("Failed to get rank list: %v", err)
				}
			}
		})
	}
}

// BenchmarkGetPlayerRank 测试获取玩家排名的性能
func BenchmarkGetPlayerRank(b *testing.B) {
	config := BenchmarkConfig{
		RankType:      1,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
		PlayerCount:   5000,
		IsRealtime:    true,
	}

	store, rankConfig := createTestStore(&config)
	populateStore(store, rankConfig, config.PlayerCount)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		playerID := uint64(rand.Intn(config.PlayerCount) + 1)
		_, _, err := store.GetPlayerRank(rankConfig.RankType, playerID)
		if err != nil {
			b.Fatalf("Failed to get player rank: %v", err)
		}
	}
}

// BenchmarkPlayerInfoManager 测试玩家信息管理器的性能
func BenchmarkPlayerInfoManager(b *testing.B) {
	store, _ := createTestStore(&BenchmarkConfig{RankType: 1, MinScoreLimit: 100, MaxRankLimit: 10000})

	// 使用反射或其他方式获取PlayerInfoManager，这里假设有GetPlayerInfoManager方法
	// 如果没有，我们需要创建一个新的PlayerInfoManager
	pim := rank.NewPlayerInfoManager()

	// 预填充一些玩家信息
	for i := 0; i < 1000; i++ {
		playerInfo := &rank.PlayerInfo{
			PlayerID:   uint64(i + 1),
			PlayerName: fmt.Sprintf("Player%d", i+1),
			Level:      int32(10 + rand.Intn(90)),
			Icon:       int32(1 + rand.Intn(10)),
		}
		pim.UpdatePlayerInfo(playerInfo)
	}

	b.Run("UpdatePlayerInfo", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			playerInfo := &rank.PlayerInfo{
				PlayerID:   uint64(i + 1001),
				PlayerName: fmt.Sprintf("Player%d", i+1001),
				Level:      int32(10 + rand.Intn(90)),
				Icon:       int32(1 + rand.Intn(10)),
			}
			pim.UpdatePlayerInfo(playerInfo)
		}
	})

	b.Run("GetPlayerInfo", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			playerID := uint64(rand.Intn(1000) + 1)
			pim.GetPlayerInfo(playerID)
		}
	})

	b.Run("GetPlayerInfoBatch", func(b *testing.B) {
		playerIDs := make([]uint64, 50)
		for i := range playerIDs {
			playerIDs[i] = uint64(rand.Intn(1000) + 1)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			pim.GetPlayerInfoBatch(playerIDs)
		}
	})
}

// =============================================================================
// 并发性能测试
// =============================================================================

// BenchmarkConcurrentUpdates 测试并发更新的性能
func BenchmarkConcurrentUpdates(b *testing.B) {
	config := BenchmarkConfig{
		RankType:      1,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
		PlayerCount:   1000,
		IsRealtime:    true,
	}

	store, rankConfig := createTestStore(&config)
	populateStore(store, rankConfig, config.PlayerCount)

	concurrencyLevels := []int{1, 2, 4, 8, 16, 32}

	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("Concurrency_%d", concurrency), func(b *testing.B) {
			b.SetParallelism(concurrency)
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					playerID := uint64(rand.Intn(config.PlayerCount*2) + 1)
					score := int32(1000 + rand.Intn(9000))
					entry := generateTestEntry(playerID, score)

					_, _, err := store.UpdateEntry(rankConfig.RankType, entry, rankConfig)
					if err != nil {
						b.Fatalf("Failed to update entry: %v", err)
					}
				}
			})
		})
	}
}

// BenchmarkConcurrentReads 测试并发读取的性能
func BenchmarkConcurrentReads(b *testing.B) {
	config := BenchmarkConfig{
		RankType:      1,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
		PlayerCount:   5000,
		IsRealtime:    true,
	}

	store, rankConfig := createTestStore(&config)
	populateStore(store, rankConfig, config.PlayerCount)

	concurrencyLevels := []int{1, 2, 4, 8, 16, 32, 64}

	for _, concurrency := range concurrencyLevels {
		b.Run(fmt.Sprintf("GetRankList_Concurrency_%d", concurrency), func(b *testing.B) {
			b.SetParallelism(concurrency)
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					start := int32(rand.Intn(4900) + 1)
					count := int32(rand.Intn(100) + 1)
					_, _, err := store.GetRankList(rankConfig.RankType, start, count)
					if err != nil {
						b.Fatalf("Failed to get rank list: %v", err)
					}
				}
			})
		})

		b.Run(fmt.Sprintf("GetPlayerRank_Concurrency_%d", concurrency), func(b *testing.B) {
			b.SetParallelism(concurrency)
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					playerID := uint64(rand.Intn(config.PlayerCount) + 1)
					_, _, err := store.GetPlayerRank(rankConfig.RankType, playerID)
					if err != nil {
						b.Fatalf("Failed to get player rank: %v", err)
					}
				}
			})
		})
	}
}

// BenchmarkMixedReadWrite 测试混合读写的性能
func BenchmarkMixedReadWrite(b *testing.B) {
	config := BenchmarkConfig{
		RankType:      1,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
		PlayerCount:   2000,
		IsRealtime:    true,
	}

	store, rankConfig := createTestStore(&config)
	populateStore(store, rankConfig, config.PlayerCount)

	readWriteRatios := []struct {
		name      string
		readRatio float32
	}{
		{"Read90Write10", 0.9},
		{"Read70Write30", 0.7},
		{"Read50Write50", 0.5},
		{"Read30Write70", 0.3},
	}

	for _, ratio := range readWriteRatios {
		b.Run(ratio.name, func(b *testing.B) {
			b.SetParallelism(8)
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					if rand.Float32() < ratio.readRatio {
						// 读操作
						if rand.Float32() < 0.5 {
							// GetRankList
							start := int32(rand.Intn(1900) + 1)
							count := int32(rand.Intn(100) + 1)
							store.GetRankList(rankConfig.RankType, start, count)
						} else {
							// GetPlayerRank
							playerID := uint64(rand.Intn(config.PlayerCount) + 1)
							store.GetPlayerRank(rankConfig.RankType, playerID)
						}
					} else {
						// 写操作
						playerID := uint64(rand.Intn(config.PlayerCount*2) + 1)
						score := int32(1000 + rand.Intn(9000))
						entry := generateTestEntry(playerID, score)
						store.UpdateEntry(rankConfig.RankType, entry, rankConfig)
					}
				}
			})
		})
	}
}

// BenchmarkMultipleRankTypes 测试多个排行榜类型的并发性能
func BenchmarkMultipleRankTypes(b *testing.B) {
	store := rank.NewMemoryStore()
	rankTypes := []int32{1, 2, 3, 4, 5}
	configs := make(map[int32]*rank.Config)

	for _, rankType := range rankTypes {
		config := &rank.Config{
			RankType:      rankType,
			MinScoreLimit: 100,
			MaxRankLimit:  5000,
			IsRealtime:    true,
			MaxQueryLimit: 1000,
			ShowRankLimit: 100,
		}
		config.InitializeDynamicLimit()
		configs[rankType] = config
	}

	store.InitializeRankTypes(rankTypes)

	// 预填充数据
	for _, rankType := range rankTypes {
		for i := 0; i < 1000; i++ {
			playerID := uint64(i + 1)
			score := int32(1000 + rand.Intn(9000))
			entry := generateTestEntry(playerID, score)
			store.UpdateEntry(rankType, entry, configs[rankType])
		}
	}

	b.ResetTimer()
	b.SetParallelism(16)
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			rankType := rankTypes[rand.Intn(len(rankTypes))]
			config := configs[rankType]

			if rand.Float32() < 0.3 {
				// 写操作
				playerID := uint64(rand.Intn(2000) + 1)
				score := int32(1000 + rand.Intn(9000))
				entry := generateTestEntry(playerID, score)
				store.UpdateEntry(rankType, entry, config)
			} else {
				// 读操作
				if rand.Float32() < 0.5 {
					start := int32(rand.Intn(900) + 1)
					count := int32(rand.Intn(100) + 1)
					store.GetRankList(rankType, start, count)
				} else {
					playerID := uint64(rand.Intn(1000) + 1)
					store.GetPlayerRank(rankType, playerID)
				}
			}
		}
	})
}
