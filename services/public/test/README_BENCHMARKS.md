# 排行榜系统基准测试和性能测试

本目录包含了排行榜系统的全面基准测试和性能测试套件，用于评估系统在各种负载条件下的性能表现。

## 测试文件结构

```
services/public/test/
├── rank_benchmark_test.go      # 主要基准测试文件
├── run_benchmarks.sh          # Linux/Mac 测试运行脚本
├── run_benchmarks.bat         # Windows 测试运行脚本
├── README_BENCHMARKS.md       # 本文档
└── benchmark_results/         # 测试结果输出目录（运行后生成）
```

## 测试覆盖范围

### 1. 基本操作基准测试
- **BenchmarkUpdateEntry**: 测试更新排行榜条目的性能
- **BenchmarkUpdateEntryWithExistingData**: 测试在已有数据基础上更新的性能
- **BenchmarkGetRankList**: 测试获取排行榜列表的性能
- **BenchmarkGetPlayerRank**: 测试获取玩家排名的性能
- **BenchmarkPlayerInfoManager**: 测试玩家信息管理器的性能

### 2. 并发性能测试
- **BenchmarkConcurrentUpdates**: 测试并发更新操作的性能
- **BenchmarkConcurrentReads**: 测试并发读取操作的性能
- **BenchmarkMixedReadWrite**: 测试混合读写操作的性能
- **BenchmarkMultipleRankTypes**: 测试多个排行榜类型的并发性能

### 3. 内存使用和可扩展性测试
- **BenchmarkMemoryUsage**: 测试不同玩家数量下的内存使用情况
- **BenchmarkScalability**: 测试系统在不同规模下的性能表现
- **BenchmarkDynamicMinScoreLimitImpact**: 测试动态最低分数限制对性能的影响

### 4. 压力测试
- **BenchmarkHighFrequencyUpdates**: 测试高频更新的性能
- **BenchmarkLargeLeaderboard**: 测试大型排行榜（100万条记录）的性能
- **BenchmarkBatchOperations**: 测试批量操作的性能

### 5. 综合性能测试
- **BenchmarkComprehensivePerformance**: 综合测试各种操作的性能

## 快速开始

### 运行所有基准测试

**Linux/Mac:**
```bash
cd services/public/test
./run_benchmarks.sh
```

**Windows:**
```cmd
cd services\public\test
run_benchmarks.bat
```

### 运行特定测试

```bash
# 只运行基本操作测试
go test -bench="BenchmarkUpdateEntry|BenchmarkGetRankList" -benchmem

# 只运行并发测试
go test -bench="BenchmarkConcurrent" -benchmem

# 运行内存测试
go test -bench="BenchmarkMemory" -benchmem

# 运行压力测试
go test -bench="BenchmarkHighFrequency|BenchmarkLarge" -benchmem
```

### 自定义测试参数

```bash
# 设置测试时间为30秒
go test -bench="BenchmarkUpdateEntry" -benchtime=30s -benchmem

# 启用CPU性能分析
go test -bench="BenchmarkUpdateEntry" -cpuprofile=cpu.prof -benchmem

# 启用内存性能分析
go test -bench="BenchmarkUpdateEntry" -memprofile=mem.prof -benchmem

# 设置并发度
go test -bench="BenchmarkConcurrent" -cpu=1,2,4,8 -benchmem
```

## 测试配置

### BenchmarkConfig 结构
```go
type BenchmarkConfig struct {
    RankType      int32  // 排行榜类型
    MinScoreLimit int32  // 最低分数限制
    MaxRankLimit  int32  // 最大排行数量
    PlayerCount   int    // 玩家数量
    IsRealtime    bool   // 是否实时排行榜
}
```

### 常用测试场景配置

1. **小型排行榜** (1000名玩家)
2. **中型排行榜** (10000名玩家)
3. **大型排行榜** (100000名玩家)
4. **超大型排行榜** (1000000名玩家)

## 性能指标解读

### 基准测试输出格式
```
BenchmarkUpdateEntry/MaxRank_1000-8    50000    25000 ns/op    1024 B/op    10 allocs/op
```

- `50000`: 执行次数
- `25000 ns/op`: 每次操作平均耗时（纳秒）
- `1024 B/op`: 每次操作平均内存分配（字节）
- `10 allocs/op`: 每次操作平均内存分配次数

### 关键性能指标

1. **吞吐量**: 每秒操作数 (ops/sec)
2. **延迟**: 平均响应时间 (ms)
3. **内存使用**: 内存分配量 (MB)
4. **并发性能**: 不同并发度下的性能表现

## 性能优化建议

### 1. 锁优化
- 当前系统已实现每个排行榜独立锁
- 避免全局锁竞争
- 读写锁分离优化

### 2. 内存优化
- 玩家信息共享存储
- 避免重复数据
- 合理的内存预分配

### 3. 算法优化
- 动态MinScoreLimit减少无效更新
- 高效的排序算法
- 批量操作支持

### 4. 并发优化
- 合理的并发度设置
- 避免锁竞争
- 异步处理支持

## 故障排除

### 常见问题

1. **测试超时**
   - 增加 `-timeout` 参数
   - 减少测试数据量
   - 检查系统资源

2. **内存不足**
   - 减少大型测试的数据量
   - 增加系统内存
   - 启用垃圾回收优化

3. **测试不稳定**
   - 增加测试运行次数 (`-count`)
   - 固定随机种子
   - 检查系统负载

### 调试技巧

```bash
# 详细输出
go test -bench="BenchmarkUpdateEntry" -v

# 生成性能分析文件
go test -bench="BenchmarkUpdateEntry" -cpuprofile=cpu.prof -memprofile=mem.prof

# 分析性能文件
go tool pprof cpu.prof
go tool pprof mem.prof
```

## 持续集成

建议在CI/CD流程中集成性能测试：

```yaml
# GitHub Actions 示例
- name: Run Benchmark Tests
  run: |
    cd services/public/test
    go test -bench=. -benchmem -count=3 > benchmark_results.txt
    
- name: Upload Benchmark Results
  uses: actions/upload-artifact@v2
  with:
    name: benchmark-results
    path: services/public/test/benchmark_results.txt
```

## 贡献指南

添加新的基准测试时，请遵循以下规范：

1. 测试函数命名：`BenchmarkXxx`
2. 使用 `BenchmarkConfig` 结构配置测试
3. 包含内存基准测试：`-benchmem`
4. 添加适当的测试文档
5. 考虑不同规模的测试场景

## 相关文档

- [Go 基准测试官方文档](https://golang.org/pkg/testing/#hdr-Benchmarks)
- [性能分析工具 pprof](https://golang.org/pkg/net/http/pprof/)
- [排行榜系统设计文档](../docs/leaderboard_design.md)
