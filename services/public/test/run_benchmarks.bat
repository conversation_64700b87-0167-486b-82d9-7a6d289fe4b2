@echo off
REM 排行榜基准测试运行脚本 (Windows版本)
REM 使用方法: run_benchmarks.bat [benchmark_time] [cpu_profile] [mem_profile]

setlocal enabledelayedexpansion

REM 默认配置
set BENCHMARK_TIME=%1
if "%BENCHMARK_TIME%"=="" set BENCHMARK_TIME=10s

set CPU_PROFILE=%2
if "%CPU_PROFILE%"=="" set CPU_PROFILE=false

set MEM_PROFILE=%3
if "%MEM_PROFILE%"=="" set MEM_PROFILE=false

set OUTPUT_DIR=benchmark_results

REM 创建输出目录
if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

echo === 排行榜性能基准测试 ===
echo 测试时间: %BENCHMARK_TIME%
echo 输出目录: %OUTPUT_DIR%
echo CPU分析: %CPU_PROFILE%
echo 内存分析: %MEM_PROFILE%
echo ================================

echo 运行基本操作基准测试...
go test -bench="BenchmarkUpdateEntry|BenchmarkGetRankList|BenchmarkGetPlayerRank|BenchmarkPlayerInfoManager" -benchtime=%BENCHMARK_TIME% -benchmem -count=3 -timeout=30m > %OUTPUT_DIR%\basic_operations.txt 2>&1

echo 运行并发性能测试...
go test -bench="BenchmarkConcurrent|BenchmarkMixed|BenchmarkMultiple" -benchtime=%BENCHMARK_TIME% -benchmem -count=3 -timeout=30m > %OUTPUT_DIR%\concurrency_tests.txt 2>&1

echo 运行内存和可扩展性测试...
go test -bench="BenchmarkMemory|BenchmarkScalability|BenchmarkDynamic" -benchtime=%BENCHMARK_TIME% -benchmem -count=3 -timeout=30m > %OUTPUT_DIR%\memory_scalability.txt 2>&1

echo 运行压力测试...
go test -bench="BenchmarkHighFrequency|BenchmarkLarge|BenchmarkBatch" -benchtime=%BENCHMARK_TIME% -benchmem -count=3 -timeout=60m > %OUTPUT_DIR%\stress_tests.txt 2>&1

REM 如果启用了性能分析
if "%CPU_PROFILE%"=="true" (
    echo 运行CPU性能分析...
    go test -bench="BenchmarkComprehensive" -benchtime=%BENCHMARK_TIME% -cpuprofile=%OUTPUT_DIR%\cpu.prof -timeout=30m > %OUTPUT_DIR%\cpu_profile.txt 2>&1
)

if "%MEM_PROFILE%"=="true" (
    echo 运行内存性能分析...
    go test -bench="BenchmarkComprehensive" -benchtime=%BENCHMARK_TIME% -memprofile=%OUTPUT_DIR%\mem.prof -timeout=30m > %OUTPUT_DIR%\mem_profile.txt 2>&1
)

echo ================================
echo 基准测试完成！
echo 结果保存在: %OUTPUT_DIR%\
echo.
echo 查看结果:
echo   基本操作: type %OUTPUT_DIR%\basic_operations.txt
echo   并发测试: type %OUTPUT_DIR%\concurrency_tests.txt
echo   内存测试: type %OUTPUT_DIR%\memory_scalability.txt
echo   压力测试: type %OUTPUT_DIR%\stress_tests.txt

if "%CPU_PROFILE%"=="true" (
    echo   CPU分析: go tool pprof %OUTPUT_DIR%\cpu.prof
)

if "%MEM_PROFILE%"=="true" (
    echo   内存分析: go tool pprof %OUTPUT_DIR%\mem.prof
)

echo.
echo 生成性能报告...

REM 创建性能报告
(
echo # 排行榜系统性能测试报告
echo.
echo ## 测试概述
echo.
echo 本报告包含了排行榜系统的全面性能测试结果，涵盖以下几个方面：
echo.
echo 1. **基本操作性能**: UpdateEntry, GetRankList, GetPlayerRank, PlayerInfoManager
echo 2. **并发性能**: 多线程读写操作性能
echo 3. **内存使用**: 不同规模下的内存占用情况
echo 4. **可扩展性**: 系统在不同数据量下的性能表现
echo 5. **压力测试**: 高频更新和大型排行榜的性能
echo.
echo ## 测试环境
echo.
echo - 测试时间: %date% %time%
echo - 基准测试时长: %BENCHMARK_TIME%
echo.
echo ## 关键性能指标
echo.
echo ### 基本操作性能
echo 详见: %OUTPUT_DIR%\basic_operations.txt
echo.
echo ### 并发性能
echo 详见: %OUTPUT_DIR%\concurrency_tests.txt
echo.
echo ### 内存使用情况
echo 详见: %OUTPUT_DIR%\memory_scalability.txt
echo.
echo ### 压力测试结果
echo 详见: %OUTPUT_DIR%\stress_tests.txt
echo.
echo ## 性能优化建议
echo.
echo 1. **锁优化**: 当前系统已经实现了每个排行榜独立锁，减少了锁竞争
echo 2. **内存优化**: 玩家信息共享存储，避免重复数据
echo 3. **动态限制**: 动态MinScoreLimit有效减少了无效更新
echo 4. **批量操作**: 考虑实现批量更新接口以提高吞吐量
echo.
echo ## 结论
echo.
echo 排行榜系统在当前设计下表现良好，能够支持高并发读写操作。
echo 建议在生产环境中根据实际负载情况调整相关参数。
) > %OUTPUT_DIR%\performance_summary.md

echo 性能报告已生成: %OUTPUT_DIR%\performance_summary.md

pause
