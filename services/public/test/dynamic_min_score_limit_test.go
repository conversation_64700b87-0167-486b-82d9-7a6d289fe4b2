package test

import (
	"fmt"
	"kairo_paradise_server/services/public/internal/module/rank"
	"testing"
)

// TestDynamicMinScoreLimit 测试动态最低分数限制功能
func TestDynamicMinScoreLimit(t *testing.T) {
	t.Run("TestBasicDynamicUpdate", func(t *testing.T) {
		// 创建独立的内存存储和配置
		store := rank.NewMemoryStore()
		config := &rank.Config{
			RankType:      1,
			MinScoreLimit: 100, // 静态最低分数限制
			MaxRankLimit:  5,   // 最多5名，便于测试
			IsRealtime:    true,
		}
		config.InitializeDynamicLimit()
		store.InitializeRankTypes([]int32{1})

		testBasicDynamicUpdate(t, store, config)
	})

	t.Run("TestFullRankDynamicUpdate", func(t *testing.T) {
		// 创建独立的内存存储和配置
		store := rank.NewMemoryStore()
		config := &rank.Config{
			RankType:      1,
			MinScoreLimit: 100, // 静态最低分数限制
			MaxRankLimit:  5,   // 最多5名，便于测试
			IsRealtime:    true,
		}
		config.InitializeDynamicLimit()
		store.InitializeRankTypes([]int32{1})

		testFullRankDynamicUpdate(t, store, config)
	})

	t.Run("TestMinLimitCanDecrease", func(t *testing.T) {
		// 创建独立的内存存储和配置
		store := rank.NewMemoryStore()
		config := &rank.Config{
			RankType:      1,
			MinScoreLimit: 100, // 静态最低分数限制
			MaxRankLimit:  5,   // 最多5名，便于测试
			IsRealtime:    true,
		}
		config.InitializeDynamicLimit()
		store.InitializeRankTypes([]int32{1})

		testMinLimitCanDecrease(t, store, config)
	})

	t.Run("TestMinLimitResetWhenNotFull", func(t *testing.T) {
		// 创建独立的内存存储和配置
		store := rank.NewMemoryStore()
		config := &rank.Config{
			RankType:      1,
			MinScoreLimit: 100, // 静态最低分数限制
			MaxRankLimit:  5,   // 最多5名，便于测试
			IsRealtime:    true,
		}
		config.InitializeDynamicLimit()
		store.InitializeRankTypes([]int32{1})

		testMinLimitResetWhenNotFull(t, store, config)
	})
}

// testBasicDynamicUpdate 测试基本的动态更新功能
func testBasicDynamicUpdate(t *testing.T, store *rank.MemoryStore, config *rank.Config) {
	// 添加一些玩家，但不满员
	players := []struct {
		playerID uint64
		score    int32
	}{
		{1001, 500},
		{1002, 400},
		{1003, 300},
	}

	for _, player := range players {
		entry := rank.NewEntry(player.playerID, fmt.Sprintf("Player%d", player.playerID), 10, 1, player.score)
		_, _, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", player.playerID, err)
		}
	}

	// 排行榜未满员，MinScoreLimit应该保持不变
	if config.GetEffectiveMinScoreLimit() != 100 {
		t.Errorf("Expected min limit to remain 100 when rank not full, got %d", config.GetEffectiveMinScoreLimit())
	}
}

// testFullRankDynamicUpdate 测试排行榜满员时的动态更新
func testFullRankDynamicUpdate(t *testing.T, store *rank.MemoryStore, config *rank.Config) {

	// 添加足够的玩家使排行榜满员
	players := []struct {
		playerID uint64
		score    int32
	}{
		{2001, 500},
		{2002, 400},
		{2003, 300},
		{2004, 250},
		{2005, 200}, // 第5名，应该成为新的MinScoreLimit
	}

	for _, player := range players {
		entry := rank.NewEntry(player.playerID, fmt.Sprintf("Player%d", player.playerID), 10, 1, player.score)
		_, _, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", player.playerID, err)
		}
	}

	// 排行榜满员，MinScoreLimit应该更新为最后一名的分数
	expectedMinLimit := int32(200) // 最后一名的分数
	if config.GetEffectiveMinScoreLimit() != expectedMinLimit {
		t.Errorf("Expected min limit to be %d when rank is full, got %d", expectedMinLimit, config.GetEffectiveMinScoreLimit())
	}

	// 添加一个分数低于新MinScoreLimit的玩家，应该无法上榜
	lowScoreEntry := rank.NewEntry(2006, "Player2006", 10, 1, 150)
	newRank, oldRank, err := store.UpdateEntry(config.RankType, lowScoreEntry, config)
	if err != nil {
		t.Fatalf("Failed to update entry for low score player: %v", err)
	}

	if newRank != -1 || oldRank != -1 {
		t.Errorf("Expected low score player not to be ranked, got newRank=%d, oldRank=%d", newRank, oldRank)
	}

	// 添加一个分数高于新MinScoreLimit的玩家，应该能够上榜
	highScoreEntry := rank.NewEntry(2007, "Player2007", 10, 1, 350)
	newRank, oldRank, err = store.UpdateEntry(config.RankType, highScoreEntry, config)
	if err != nil {
		t.Fatalf("Failed to update entry for high score player: %v", err)
	}

	if newRank == -1 {
		t.Errorf("Expected high score player to be ranked, got newRank=%d", newRank)
	}

	// MinScoreLimit应该更新为新的最后一名分数
	// 由于添加了高分玩家，最后一名的分数应该比之前的200要高
	newMinLimit := config.GetEffectiveMinScoreLimit()
	if newMinLimit <= expectedMinLimit {
		t.Errorf("Expected min limit to increase after high score player joined, got %d", newMinLimit)
	}
}

// testMinLimitCanDecrease 测试MinScoreLimit可以降低
func testMinLimitCanDecrease(t *testing.T, store *rank.MemoryStore, config *rank.Config) {
	// 先添加一些高分玩家使排行榜满员
	highScorePlayers := []struct {
		playerID uint64
		score    int32
	}{
		{3001, 500},
		{3002, 450},
		{3003, 400},
		{3004, 350},
		{3005, 300}, // 第5名，动态限制应该是300
	}

	for _, player := range highScorePlayers {
		entry := rank.NewEntry(player.playerID, fmt.Sprintf("Player%d", player.playerID), 10, 1, player.score)
		_, _, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", player.playerID, err)
		}
	}

	// 验证动态限制是300
	if config.GetEffectiveMinScoreLimit() != 300 {
		t.Errorf("Expected min limit to be 300, got %d", config.GetEffectiveMinScoreLimit())
	}

	// 现在更新所有玩家的分数，使其都降低
	lowerScorePlayers := []struct {
		playerID uint64
		score    int32
	}{
		{3001, 250}, // 降低到250
		{3002, 200}, // 降低到200
		{3003, 150}, // 降低到150
		{3004, 120}, // 降低到120
		{3005, 110}, // 降低到110，新的最后一名
	}

	for _, player := range lowerScorePlayers {
		entry := rank.NewEntry(player.playerID, fmt.Sprintf("Player%d", player.playerID), 10, 1, player.score)
		_, _, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", player.playerID, err)
		}
	}

	// 动态限制应该降低到110（最后一名的分数）
	if config.GetEffectiveMinScoreLimit() != 110 {
		t.Errorf("Expected min limit to decrease to 110, got %d", config.GetEffectiveMinScoreLimit())
	}

	// 现在一个分数为115的玩家应该能够上榜
	newPlayer := rank.NewEntry(3006, "Player3006", 10, 1, 115)
	newRank, _, err := store.UpdateEntry(config.RankType, newPlayer, config)
	if err != nil {
		t.Fatalf("Failed to update entry for new player: %v", err)
	}

	if newRank == -1 {
		t.Errorf("Expected player with score 115 to be ranked, but got -1")
	}

	// 动态限制应该等于新的最后一名分数（115）
	if config.GetEffectiveMinScoreLimit() != 115 {
		t.Errorf("Expected min limit to be 115 after new player joined, got %d", config.GetEffectiveMinScoreLimit())
	}
}

// testMinLimitResetWhenNotFull 测试排行榜未满员时动态限制重置为静态限制
func testMinLimitResetWhenNotFull(t *testing.T, store *rank.MemoryStore, config *rank.Config) {
	// 先添加足够的玩家使排行榜满员
	players := []struct {
		playerID uint64
		score    int32
	}{
		{4001, 500},
		{4002, 450},
		{4003, 400},
		{4004, 350},
		{4005, 300}, // 第5名，动态限制应该是300
	}

	for _, player := range players {
		entry := rank.NewEntry(player.playerID, fmt.Sprintf("Player%d", player.playerID), 10, 1, player.score)
		_, _, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", player.playerID, err)
		}
	}

	// 验证动态限制是300
	if config.GetEffectiveMinScoreLimit() != 300 {
		t.Errorf("Expected min limit to be 300, got %d", config.GetEffectiveMinScoreLimit())
	}

	// 移除一个玩家，使排行榜不再满员
	// 通过更新玩家分数为很低的值来模拟移除
	lowScoreEntry := rank.NewEntry(4005, "Player4005", 10, 1, 50) // 低于静态限制100
	_, _, err := store.UpdateEntry(config.RankType, lowScoreEntry, config)
	if err != nil {
		t.Fatalf("Failed to update entry to remove player: %v", err)
	}

	// 现在排行榜应该只有4个玩家，动态限制应该重置为静态限制100
	if config.GetEffectiveMinScoreLimit() != 100 {
		t.Errorf("Expected min limit to reset to static limit 100 when rank not full, got %d", config.GetEffectiveMinScoreLimit())
	}
}

// TestConfigMethods 测试Config的相关方法
func TestConfigMethods(t *testing.T) {
	config := &rank.Config{
		RankType:      1,
		MinScoreLimit: 100,
	}

	// 测试初始化
	config.InitializeDynamicLimit()

	if config.StaticMinScoreLimit != 100 {
		t.Errorf("Expected static min limit to be 100, got %d", config.StaticMinScoreLimit)
	}

	if config.DynamicMinScoreLimit != 100 {
		t.Errorf("Expected dynamic min limit to be 100, got %d", config.DynamicMinScoreLimit)
	}

	if !config.EnableDynamicLimit {
		t.Error("Expected dynamic limit to be enabled")
	}

	// 测试更新动态限制
	config.UpdateDynamicMinScoreLimit(200)

	if config.DynamicMinScoreLimit != 200 {
		t.Errorf("Expected dynamic min limit to be 200, got %d", config.DynamicMinScoreLimit)
	}

	// 测试获取有效限制
	if config.GetEffectiveMinScoreLimit() != 200 {
		t.Errorf("Expected effective min limit to be 200, got %d", config.GetEffectiveMinScoreLimit())
	}

	// 测试禁用动态限制
	config.EnableDynamicLimit = false
	config.UpdateDynamicMinScoreLimit(300)

	// 动态限制被禁用，应该不会更新
	if config.DynamicMinScoreLimit != 200 {
		t.Errorf("Expected dynamic min limit to remain 200 when disabled, got %d", config.DynamicMinScoreLimit)
	}

	// 有效限制应该回到静态限制
	if config.GetEffectiveMinScoreLimit() != 100 {
		t.Errorf("Expected effective min limit to be 100 when dynamic disabled, got %d", config.GetEffectiveMinScoreLimit())
	}
}

// BenchmarkDynamicMinScoreLimit 性能测试
func BenchmarkDynamicMinScoreLimit(b *testing.B) {
	store := rank.NewMemoryStore()
	config := &rank.Config{
		RankType:      1,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
		IsRealtime:    true,
	}
	config.InitializeDynamicLimit()
	store.InitializeRankTypes([]int32{1})

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		playerID := uint64(i + 1)
		score := int32(1000 - i%1000) // 分数从1000递减
		entry := rank.NewEntry(playerID, fmt.Sprintf("Player%d", playerID), 10, 1, score)

		_, _, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			b.Fatalf("Failed to update entry: %v", err)
		}
	}
}
