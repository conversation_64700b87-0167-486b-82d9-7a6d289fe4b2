package test

import (
	"fmt"
	"kairo_paradise_server/services/public/internal/module/rank"
	"math/rand"
	"sync"
	"testing"
	"time"
)

// TestPlayerInfoManagerOptimization 测试玩家信息管理器优化效果
func TestPlayerInfoManagerOptimization(t *testing.T) {
	t.Run("OriginalPlayerInfoManager", func(t *testing.T) {
		testPlayerInfoManagerPerformance(t, "Original", createOriginalPlayerInfoManager())
	})

	t.Run("OptimizedPlayerInfoManager", func(t *testing.T) {
		testPlayerInfoManagerPerformance(t, "Optimized", createOptimizedPlayerInfoManager())
	})
}

// PlayerInfoManagerInterface 玩家信息管理器接口
type PlayerInfoManagerInterface interface {
	UpdatePlayerInfo(playerInfo *rank.PlayerInfo)
	GetPlayerInfo(playerID uint64) *rank.PlayerInfo
	GetPlayerInfoBatch(playerIDs []uint64) map[uint64]*rank.PlayerInfo
	HasPlayerInfo(playerID uint64) bool
	GetPlayerCount() int64
	GetUpdateCount() int64
}

// createOriginalPlayerInfoManager 创建原始版本的玩家信息管理器
func createOriginalPlayerInfoManager() PlayerInfoManagerInterface {
	return rank.NewPlayerInfoManager()
}

// createOptimizedPlayerInfoManager 创建优化版本的玩家信息管理器
func createOptimizedPlayerInfoManager() PlayerInfoManagerInterface {
	return rank.NewOptimizedPlayerInfoManager()
}

// testPlayerInfoManagerPerformance 测试玩家信息管理器性能
func testPlayerInfoManagerPerformance(t *testing.T, name string, manager PlayerInfoManagerInterface) {
	// 测试参数
	playerCount := 100000
	concurrency := 50
	operationsPerGoroutine := 2000

	// 预先创建测试数据
	testPlayers := make([]*rank.PlayerInfo, playerCount)
	for i := 0; i < playerCount; i++ {
		testPlayers[i] = &rank.PlayerInfo{
			PlayerID:   uint64(i + 1),
			PlayerName: fmt.Sprintf("Player%d", i+1),
			Level:      int32(rand.Intn(100) + 1),
			Icon:       int32(rand.Intn(10) + 1),
			Prosperity: int32(rand.Intn(10000)),
		}
	}

	// 预先插入一些数据
	for i := 0; i < playerCount/2; i++ {
		manager.UpdatePlayerInfo(testPlayers[i])
	}

	t.Logf("Testing %s PlayerInfoManager performance", name)

	start := time.Now()

	var wg sync.WaitGroup
	wg.Add(concurrency)

	for i := 0; i < concurrency; i++ {
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < operationsPerGoroutine; j++ {
				playerID := uint64(rand.Intn(playerCount) + 1)

				// 混合操作：读多写少（80%读，20%写）
				if rand.Float32() < 0.8 {
					// 读操作
					switch rand.Intn(3) {
					case 0:
						_ = manager.GetPlayerInfo(playerID)
					case 1:
						_ = manager.HasPlayerInfo(playerID)
					case 2:
						// 批量读取
						batchIDs := make([]uint64, 10)
						for k := 0; k < 10; k++ {
							batchIDs[k] = uint64(rand.Intn(playerCount) + 1)
						}
						_ = manager.GetPlayerInfoBatch(batchIDs)
					}
				} else {
					// 写操作
					playerIndex := rand.Intn(playerCount)
					manager.UpdatePlayerInfo(testPlayers[playerIndex])
				}
			}
		}(i)
	}

	wg.Wait()
	duration := time.Since(start)

	totalOperations := concurrency * operationsPerGoroutine
	opsPerSecond := float64(totalOperations) / duration.Seconds()

	t.Logf("%s PlayerInfoManager Results:", name)
	t.Logf("  Total operations: %d", totalOperations)
	t.Logf("  Duration: %v", duration)
	t.Logf("  Operations per second: %.2f", opsPerSecond)
	t.Logf("  Average operation time: %v", duration/time.Duration(totalOperations))
	t.Logf("  Final player count: %d", manager.GetPlayerCount())
	t.Logf("  Total updates: %d", manager.GetUpdateCount())
}

// BenchmarkOriginalPlayerInfoManager 基准测试原始版本
func BenchmarkOriginalPlayerInfoManager(b *testing.B) {
	manager := createOriginalPlayerInfoManager()

	// 预先插入一些数据
	for i := 0; i < 100000; i++ {
		manager.UpdatePlayerInfo(&rank.PlayerInfo{
			PlayerID:   uint64(i + 1),
			PlayerName: fmt.Sprintf("Player%d", i+1),
			Level:      int32(i%100 + 1),
			Icon:       int32(i%10 + 1),
			Prosperity: int32(i * 10),
		})
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			playerID := uint64(i%1000 + 1)

			// 80%读操作，20%写操作
			if i%5 == 0 {
				// 写操作
				manager.UpdatePlayerInfo(&rank.PlayerInfo{
					PlayerID:   playerID,
					PlayerName: fmt.Sprintf("Player%d", playerID),
					Level:      int32(i%100 + 1),
					Icon:       int32(i%10 + 1),
					Prosperity: int32(i * 10),
				})
			} else {
				// 读操作
				_ = manager.GetPlayerInfo(playerID)
			}

			i++
		}
	})
}

// BenchmarkOptimizedPlayerInfoManager 基准测试优化版本
func BenchmarkOptimizedPlayerInfoManager(b *testing.B) {
	manager := createOptimizedPlayerInfoManager()

	// 预先插入一些数据
	for i := 0; i < 100000; i++ {
		manager.UpdatePlayerInfo(&rank.PlayerInfo{
			PlayerID:   uint64(i + 1),
			PlayerName: fmt.Sprintf("Player%d", i+1),
			Level:      int32(i%100 + 1),
			Icon:       int32(i%10 + 1),
			Prosperity: int32(i * 10),
		})
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			playerID := uint64(i%1000 + 1)

			// 80%读操作，20%写操作
			if i%5 == 0 {
				// 写操作
				manager.UpdatePlayerInfo(&rank.PlayerInfo{
					PlayerID:   playerID,
					PlayerName: fmt.Sprintf("Player%d", playerID),
					Level:      int32(i%100 + 1),
					Icon:       int32(i%10 + 1),
					Prosperity: int32(i * 10),
				})
			} else {
				// 读操作
				_ = manager.GetPlayerInfo(playerID)
			}

			i++
		}
	})
}

// TestBatchOperationOptimization 测试批量操作优化
func TestBatchOperationOptimization(t *testing.T) {
	playerCount := 10000
	batchSize := 100

	t.Run("OriginalBatchOperation", func(t *testing.T) {
		testBatchOperation(t, "Original", createOriginalPlayerInfoManager(), playerCount, batchSize)
	})

	t.Run("OptimizedBatchOperation", func(t *testing.T) {
		testBatchOperation(t, "Optimized", createOptimizedPlayerInfoManager(), playerCount, batchSize)
	})
}

// testBatchOperation 测试批量操作性能
func testBatchOperation(t *testing.T, name string, manager PlayerInfoManagerInterface, playerCount, batchSize int) {
	// 预先插入数据
	for i := 0; i < playerCount; i++ {
		manager.UpdatePlayerInfo(&rank.PlayerInfo{
			PlayerID:   uint64(i + 1),
			PlayerName: fmt.Sprintf("Player%d", i+1),
			Level:      int32(i%100 + 1),
			Icon:       int32(i%10 + 1),
			Prosperity: int32(i * 10),
		})
	}

	// 准备批量查询的ID
	batchIDs := make([]uint64, batchSize)
	for i := 0; i < batchSize; i++ {
		batchIDs[i] = uint64(rand.Intn(playerCount) + 1)
	}

	// 测试批量操作性能
	iterations := 1000
	start := time.Now()

	for i := 0; i < iterations; i++ {
		result := manager.GetPlayerInfoBatch(batchIDs)
		if len(result) == 0 {
			t.Errorf("Batch operation returned empty result")
		}
	}

	duration := time.Since(start)
	opsPerSecond := float64(iterations) / duration.Seconds()

	t.Logf("%s Batch Operation Results:", name)
	t.Logf("  Batch size: %d", batchSize)
	t.Logf("  Iterations: %d", iterations)
	t.Logf("  Duration: %v", duration)
	t.Logf("  Operations per second: %.2f", opsPerSecond)
	t.Logf("  Average batch time: %v", duration/time.Duration(iterations))
}

// TestConcurrentReadWrite 测试并发读写性能
func TestConcurrentReadWrite(t *testing.T) {
	t.Run("OriginalConcurrentReadWrite", func(t *testing.T) {
		testConcurrentReadWrite(t, "Original", createOriginalPlayerInfoManager())
	})

	t.Run("OptimizedConcurrentReadWrite", func(t *testing.T) {
		testConcurrentReadWrite(t, "Optimized", createOptimizedPlayerInfoManager())
	})
}

// testConcurrentReadWrite 测试并发读写
func testConcurrentReadWrite(t *testing.T, name string, manager PlayerInfoManagerInterface) {
	playerCount := 1000
	duration := 3 * time.Second
	readerCount := 40
	writerCount := 10

	// 预先插入数据
	for i := 0; i < playerCount; i++ {
		manager.UpdatePlayerInfo(&rank.PlayerInfo{
			PlayerID:   uint64(i + 1),
			PlayerName: fmt.Sprintf("Player%d", i+1),
			Level:      int32(i%100 + 1),
			Icon:       int32(i%10 + 1),
			Prosperity: int32(i * 10),
		})
	}

	var wg sync.WaitGroup
	var readOps, writeOps int64
	start := time.Now()

	// 启动读取器
	for i := 0; i < readerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			localReadOps := int64(0)
			for time.Since(start) < duration {
				playerID := uint64(rand.Intn(playerCount) + 1)
				_ = manager.GetPlayerInfo(playerID)
				localReadOps++
			}
			readOps += localReadOps
		}()
	}

	// 启动写入器
	for i := 0; i < writerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			localWriteOps := int64(0)
			for time.Since(start) < duration {
				playerID := uint64(rand.Intn(playerCount) + 1)
				manager.UpdatePlayerInfo(&rank.PlayerInfo{
					PlayerID:   playerID,
					PlayerName: fmt.Sprintf("Player%d", playerID),
					Level:      int32(rand.Intn(100) + 1),
					Icon:       int32(rand.Intn(10) + 1),
					Prosperity: int32(rand.Intn(10000)),
				})
				localWriteOps++
			}
			writeOps += localWriteOps
		}()
	}

	wg.Wait()
	actualDuration := time.Since(start)

	t.Logf("%s Concurrent Read/Write Results:", name)
	t.Logf("  Readers: %d, Writers: %d", readerCount, writerCount)
	t.Logf("  Duration: %v", actualDuration)
	t.Logf("  Read operations: %d (%.2f ops/sec)", readOps, float64(readOps)/actualDuration.Seconds())
	t.Logf("  Write operations: %d (%.2f ops/sec)", writeOps, float64(writeOps)/actualDuration.Seconds())
	t.Logf("  Total operations: %d (%.2f ops/sec)", readOps+writeOps, float64(readOps+writeOps)/actualDuration.Seconds())
}
