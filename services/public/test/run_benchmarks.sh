#!/bin/bash

# 排行榜基准测试运行脚本
# 使用方法: ./run_benchmarks.sh [test_pattern] [options]

set -e

# 默认配置
TEST_PATTERN=${1:-"Benchmark"}
BENCHMARK_TIME=${2:-"10s"}
CPU_PROFILE=${3:-"false"}
MEM_PROFILE=${4:-"false"}
OUTPUT_DIR="benchmark_results"

# 创建输出目录
mkdir -p $OUTPUT_DIR

echo "=== 排行榜性能基准测试 ==="
echo "测试模式: $TEST_PATTERN"
echo "测试时间: $BENCHMARK_TIME"
echo "输出目录: $OUTPUT_DIR"
echo "================================"

# 基本性能测试
echo "运行基本操作基准测试..."
go test -bench="BenchmarkUpdateEntry|BenchmarkGetRankList|BenchmarkGetPlayerRank|BenchmarkPlayerInfoManager" \
    -benchtime=$BENCHMARK_TIME \
    -benchmem \
    -count=3 \
    -timeout=30m \
    > $OUTPUT_DIR/basic_operations.txt 2>&1

echo "运行并发性能测试..."
go test -bench="BenchmarkConcurrent|BenchmarkMixed|BenchmarkMultiple" \
    -benchtime=$BENCHMARK_TIME \
    -benchmem \
    -count=3 \
    -timeout=30m \
    > $OUTPUT_DIR/concurrency_tests.txt 2>&1

echo "运行内存和可扩展性测试..."
go test -bench="BenchmarkMemory|BenchmarkScalability|BenchmarkDynamic" \
    -benchtime=$BENCHMARK_TIME \
    -benchmem \
    -count=3 \
    -timeout=30m \
    > $OUTPUT_DIR/memory_scalability.txt 2>&1

echo "运行压力测试..."
go test -bench="BenchmarkHighFrequency|BenchmarkLarge|BenchmarkBatch" \
    -benchtime=$BENCHMARK_TIME \
    -benchmem \
    -count=3 \
    -timeout=60m \
    > $OUTPUT_DIR/stress_tests.txt 2>&1

# 如果启用了性能分析
if [ "$CPU_PROFILE" = "true" ]; then
    echo "运行CPU性能分析..."
    go test -bench="BenchmarkComprehensive" \
        -benchtime=$BENCHMARK_TIME \
        -cpuprofile=$OUTPUT_DIR/cpu.prof \
        -timeout=30m \
        > $OUTPUT_DIR/cpu_profile.txt 2>&1
fi

if [ "$MEM_PROFILE" = "true" ]; then
    echo "运行内存性能分析..."
    go test -bench="BenchmarkComprehensive" \
        -benchtime=$BENCHMARK_TIME \
        -memprofile=$OUTPUT_DIR/mem.prof \
        -timeout=30m \
        > $OUTPUT_DIR/mem_profile.txt 2>&1
fi

echo "================================"
echo "基准测试完成！"
echo "结果保存在: $OUTPUT_DIR/"
echo ""
echo "查看结果:"
echo "  基本操作: cat $OUTPUT_DIR/basic_operations.txt"
echo "  并发测试: cat $OUTPUT_DIR/concurrency_tests.txt"
echo "  内存测试: cat $OUTPUT_DIR/memory_scalability.txt"
echo "  压力测试: cat $OUTPUT_DIR/stress_tests.txt"

if [ "$CPU_PROFILE" = "true" ]; then
    echo "  CPU分析: go tool pprof $OUTPUT_DIR/cpu.prof"
fi

if [ "$MEM_PROFILE" = "true" ]; then
    echo "  内存分析: go tool pprof $OUTPUT_DIR/mem.prof"
fi

echo ""
echo "生成性能报告..."
cat > $OUTPUT_DIR/performance_summary.md << 'EOF'
# 排行榜系统性能测试报告

## 测试概述

本报告包含了排行榜系统的全面性能测试结果，涵盖以下几个方面：

1. **基本操作性能**: UpdateEntry, GetRankList, GetPlayerRank, PlayerInfoManager
2. **并发性能**: 多线程读写操作性能
3. **内存使用**: 不同规模下的内存占用情况
4. **可扩展性**: 系统在不同数据量下的性能表现
5. **压力测试**: 高频更新和大型排行榜的性能

## 测试环境

- Go版本: $(go version)
- 测试时间: $(date)
- 机器配置: $(uname -a)

## 关键性能指标

### 基本操作性能
```
$(head -20 basic_operations.txt | tail -10)
```

### 并发性能
```
$(head -20 concurrency_tests.txt | tail -10)
```

### 内存使用情况
```
$(head -20 memory_scalability.txt | tail -10)
```

## 性能优化建议

1. **锁优化**: 当前系统已经实现了每个排行榜独立锁，减少了锁竞争
2. **内存优化**: 玩家信息共享存储，避免重复数据
3. **动态限制**: 动态MinScoreLimit有效减少了无效更新
4. **批量操作**: 考虑实现批量更新接口以提高吞吐量

## 结论

排行榜系统在当前设计下表现良好，能够支持高并发读写操作。
建议在生产环境中根据实际负载情况调整相关参数。

EOF

echo "性能报告已生成: $OUTPUT_DIR/performance_summary.md"
