package rank

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

// TestLockOptimization 测试锁优化效果
func TestLockOptimization(t *testing.T) {
	// 测试排行榜类型
	rankTypes := []int32{1, 2, 3, 9981, 9982, 9983}
	
	t.Run("OriginalMemoryStore", func(t *testing.T) {
		testMemoryStorePerformance(t, "Original", createOriginalMemoryStore(rankTypes))
	})
	
	t.Run("OptimizedMemoryStore", func(t *testing.T) {
		testMemoryStorePerformance(t, "Optimized", createOptimizedMemoryStore(rankTypes))
	})
}

// createOriginalMemoryStore 创建原始版本的内存存储（模拟原来的实现）
func createOriginalMemoryStore(rankTypes []int32) MemoryStoreInterface {
	store := &OriginalMemoryStore{
		rankData:    make(map[int32]*MemoryRankData),
		rankMutex:   make(map[int32]*sync.RWMutex),
		globalMutex: sync.RWMutex{},
	}
	return store
}

// createOptimizedMemoryStore 创建优化版本的内存存储
func createOptimizedMemoryStore(rankTypes []int32) MemoryStoreInterface {
	store := NewMemoryStore()
	store.InitializeRankTypes(rankTypes)
	return store
}

// MemoryStoreInterface 内存存储接口
type MemoryStoreInterface interface {
	GetRankMutex(rankType int32) *sync.RWMutex
	GetRankData(rankType int32) *MemoryRankData
}

// OriginalMemoryStore 原始版本的内存存储（用于性能对比）
type OriginalMemoryStore struct {
	rankData    map[int32]*MemoryRankData
	rankMutex   map[int32]*sync.RWMutex
	globalMutex sync.RWMutex
}

// GetRankMutex 获取排行榜锁（原始版本）
func (oms *OriginalMemoryStore) GetRankMutex(rankType int32) *sync.RWMutex {
	oms.globalMutex.RLock()
	mutex, exists := oms.rankMutex[rankType]
	oms.globalMutex.RUnlock()

	if !exists {
		oms.globalMutex.Lock()
		// 双重检查
		if mutex, exists = oms.rankMutex[rankType]; !exists {
			mutex = &sync.RWMutex{}
			oms.rankMutex[rankType] = mutex
		}
		oms.globalMutex.Unlock()
	}

	return mutex
}

// GetRankData 获取排行榜数据（原始版本）
func (oms *OriginalMemoryStore) GetRankData(rankType int32) *MemoryRankData {
	oms.globalMutex.RLock()
	data, exists := oms.rankData[rankType]
	oms.globalMutex.RUnlock()

	if !exists {
		oms.globalMutex.Lock()
		// 双重检查
		if data, exists = oms.rankData[rankType]; !exists {
			data = NewMemoryRankData()
			oms.rankData[rankType] = data
		}
		oms.globalMutex.Unlock()
	}

	return data
}

// GetRankMutex 优化版本的接口实现
func (ms *MemoryStore) GetRankMutex(rankType int32) *sync.RWMutex {
	return ms.getRankMutex(rankType)
}

// GetRankData 优化版本的接口实现
func (ms *MemoryStore) GetRankData(rankType int32) *MemoryRankData {
	return ms.getRankData(rankType)
}

// testMemoryStorePerformance 测试内存存储性能
func testMemoryStorePerformance(t *testing.T, name string, store MemoryStoreInterface) {
	rankTypes := []int32{1, 2, 3, 9981, 9982, 9983}
	concurrency := 100
	operationsPerGoroutine := 1000

	t.Logf("Testing %s MemoryStore performance", name)
	
	start := time.Now()
	
	var wg sync.WaitGroup
	wg.Add(concurrency)
	
	for i := 0; i < concurrency; i++ {
		go func(goroutineID int) {
			defer wg.Done()
			
			for j := 0; j < operationsPerGoroutine; j++ {
				rankType := rankTypes[j%len(rankTypes)]
				
				// 测试获取锁的性能
				mutex := store.GetRankMutex(rankType)
				if mutex == nil {
					t.Errorf("GetRankMutex returned nil for rankType %d", rankType)
					return
				}
				
				// 测试获取数据的性能
				data := store.GetRankData(rankType)
				if data == nil {
					t.Errorf("GetRankData returned nil for rankType %d", rankType)
					return
				}
				
				// 模拟一些读操作
				mutex.RLock()
				_ = len(data.PlayerMap)
				mutex.RUnlock()
			}
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	totalOperations := concurrency * operationsPerGoroutine * 2 // 每次循环有2个操作
	opsPerSecond := float64(totalOperations) / duration.Seconds()
	
	t.Logf("%s MemoryStore Results:", name)
	t.Logf("  Total operations: %d", totalOperations)
	t.Logf("  Duration: %v", duration)
	t.Logf("  Operations per second: %.2f", opsPerSecond)
	t.Logf("  Average operation time: %v", duration/time.Duration(totalOperations))
}

// BenchmarkOriginalMemoryStore 基准测试原始版本
func BenchmarkOriginalMemoryStore(b *testing.B) {
	rankTypes := []int32{1, 2, 3, 9981, 9982, 9983}
	store := createOriginalMemoryStore(rankTypes)
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			rankType := rankTypes[i%len(rankTypes)]
			mutex := store.GetRankMutex(rankType)
			data := store.GetRankData(rankType)
			
			mutex.RLock()
			_ = len(data.PlayerMap)
			mutex.RUnlock()
			
			i++
		}
	})
}

// BenchmarkOptimizedMemoryStore 基准测试优化版本
func BenchmarkOptimizedMemoryStore(b *testing.B) {
	rankTypes := []int32{1, 2, 3, 9981, 9982, 9983}
	store := createOptimizedMemoryStore(rankTypes)
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			rankType := rankTypes[i%len(rankTypes)]
			mutex := store.GetRankMutex(rankType)
			data := store.GetRankData(rankType)
			
			mutex.RLock()
			_ = len(data.PlayerMap)
			mutex.RUnlock()
			
			i++
		}
	})
}

// TestConcurrentAccess 测试并发访问
func TestConcurrentAccess(t *testing.T) {
	rankTypes := []int32{1, 2, 3}
	
	t.Run("OriginalConcurrentAccess", func(t *testing.T) {
		store := createOriginalMemoryStore(rankTypes)
		testConcurrentAccess(t, "Original", store, rankTypes)
	})
	
	t.Run("OptimizedConcurrentAccess", func(t *testing.T) {
		store := createOptimizedMemoryStore(rankTypes)
		testConcurrentAccess(t, "Optimized", store, rankTypes)
	})
}

// testConcurrentAccess 测试并发访问
func testConcurrentAccess(t *testing.T, name string, store MemoryStoreInterface, rankTypes []int32) {
	concurrency := 50
	duration := 2 * time.Second
	
	var wg sync.WaitGroup
	var operationCount int64
	
	start := time.Now()
	
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			
			localCount := int64(0)
			for time.Since(start) < duration {
				rankType := rankTypes[localCount%int64(len(rankTypes))]
				
				mutex := store.GetRankMutex(rankType)
				data := store.GetRankData(rankType)
				
				mutex.RLock()
				_ = len(data.PlayerMap)
				mutex.RUnlock()
				
				localCount++
			}
			
			// 使用原子操作更新总计数（这里简化处理）
			operationCount += localCount
		}(i)
	}
	
	wg.Wait()
	actualDuration := time.Since(start)
	
	opsPerSecond := float64(operationCount) / actualDuration.Seconds()
	
	t.Logf("%s Concurrent Access Results:", name)
	t.Logf("  Goroutines: %d", concurrency)
	t.Logf("  Duration: %v", actualDuration)
	t.Logf("  Total operations: %d", operationCount)
	t.Logf("  Operations per second: %.2f", opsPerSecond)
}

// TestMemoryUsage 测试内存使用情况
func TestMemoryUsage(t *testing.T) {
	rankTypes := make([]int32, 100) // 创建100个排行榜类型
	for i := 0; i < 100; i++ {
		rankTypes[i] = int32(i + 1)
	}
	
	t.Run("OriginalMemoryUsage", func(t *testing.T) {
		store := createOriginalMemoryStore(rankTypes)
		// 预热，触发所有排行榜的创建
		for _, rankType := range rankTypes {
			store.GetRankMutex(rankType)
			store.GetRankData(rankType)
		}
		t.Logf("Original store initialized with %d rank types", len(rankTypes))
	})
	
	t.Run("OptimizedMemoryUsage", func(t *testing.T) {
		store := createOptimizedMemoryStore(rankTypes)
		t.Logf("Optimized store initialized with %d rank types", len(rankTypes))
	})
}
