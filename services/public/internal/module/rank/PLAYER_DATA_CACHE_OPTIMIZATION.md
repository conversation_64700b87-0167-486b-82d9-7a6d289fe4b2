# 玩家数据缓存优化

## 优化概述

本次优化解决了玩家外观数据在多个排行榜中重复存储的问题，通过分离玩家基础数据和排名数据，显著减少了内存使用。

## 问题分析

### 优化前的问题
1. **重复存储**：每个排行榜的CacheManager都存储完整的玩家数据（PlayerName、Level等）
2. **内存浪费**：同一个玩家的外观数据在多个排行榜中存储多份
3. **数据不一致风险**：玩家数据更新时需要同步更新所有排行榜的缓存

### 优化后的改进
1. **数据分离**：将玩家基础数据和排名数据分离存储
2. **全局共享**：玩家基础数据在所有排行榜间共享
3. **内存优化**：大幅减少内存使用，特别是在多排行榜场景下

## 架构变更

### 数据结构重新设计

#### 新增PlayerDataCache（全局共享）
```go
type PlayerDataCache struct {
    PlayerID   uint64 // 玩家ID
    PlayerName string // 玩家名称
    Level      int32  // 玩家等级
    UpdateTime int64  // 缓存更新时间
}
```

#### 重新设计PlayerRankCache（排行榜特定）
```go
type PlayerRankCache struct {
    Rank       int32 // 玩家排名
    Score      int32 // 玩家分数（如繁荣度）
    UpdateTime int64 // 缓存更新时间
}
```

### Manager结构更新

```go
type Manager struct {
    // ... 原有字段

    // 新增：全局玩家数据缓存
    playerDataCache map[uint64]*PlayerDataCache
    playerDataCacheExpireTime int64 // 玩家数据缓存过期时间
}
```

### CacheManager结构更新

```go
type CacheManager struct {
    // ... 原有字段

    // 修改：只存储排名信息，不存储完整玩家数据
    playerRankCaches map[uint64]*PlayerRankCache // 简化的key结构
}
```

## 核心优化点

### 1. 数据分离存储

**玩家基础数据（全局共享）**：
- PlayerID、PlayerName、Level
- 存储在Manager的playerDataCache中
- 所有排行榜共享，避免重复存储

**排名数据（排行榜特定）**：
- Rank、Score
- 存储在各个CacheManager的playerRankCaches中
- 每个排行榜独立管理

### 2. 缓存策略优化

**玩家基础数据缓存**：
- 过期策略：不自动过期，只在排名更新时刷新
- 清理策略：手动清理或系统维护时清理

**排名数据缓存**：
- 过期时间：根据排行榜类型配置（实时榜1分钟，定时榜5分钟）
- 清理频率：每5分钟清理一次过期数据

### 3. 数据组合机制

通过`buildRankInfo`方法动态组合玩家基础数据和排名数据：

```go
func (m *Manager) buildRankInfo(playerID uint64, rankCache *PlayerRankCache) *RankInfo {
    // 获取排名数据
    if rankCache == nil || rankCache.Rank == -1 {
        return &RankInfo{Rank: -1}
    }

    // 获取玩家基础数据
    playerData := m.getPlayerDataCache(playerID)

    // 组合完整的Entry
    entry := Entry{
        PlayerID:   playerID,
        Prosperity: rankCache.Score,
        PlayerName: playerData.PlayerName, // 从全局缓存获取
        Level:      playerData.Level,      // 从全局缓存获取
    }

    return &RankInfo{Rank: rankCache.Rank, Entry: entry}
}
```

## 内存使用对比

### 优化前
假设有4个排行榜，1000个玩家：
- 每个玩家数据：~100字节（PlayerName、Level等）
- 总内存使用：4 × 1000 × 100 = 400KB

### 优化后
相同场景：
- 玩家基础数据：1000 × 60 = 60KB（全局共享）
- 排名数据：4 × 1000 × 20 = 80KB（每个排行榜）
- 总内存使用：60KB + 80KB = 140KB

**内存节省：65%**

## API变更

### CacheManager方法变更

```go
// 旧方法
GetPlayerRankCache(rankType int32, playerID uint64) *PlayerRankCache
SetPlayerRankCache(ctx context.Context, rankType int32, playerID uint64, rank int32, entry *Entry)

// 新方法
GetPlayerRankCache(playerID uint64) *PlayerRankCache
SetPlayerRankCache(playerID uint64, rank int32, score int32)
```

### Manager新增方法

```go
// 玩家数据缓存管理
getPlayerDataCache(playerID uint64) *PlayerDataCache
setPlayerDataCache(playerID uint64, playerName string, level int32)
ClearPlayerDataCache(playerID uint64)     // 清理指定玩家缓存
ClearAllPlayerDataCache()                 // 清理所有玩家缓存

// 数据组合
buildRankInfo(playerID uint64, rankCache *PlayerRankCache) *RankInfo
```

## 使用示例

### 获取玩家排名（自动数据组合）
```go
manager := GetRankManager()
rankInfo, err := manager.GetPlayerRank(ctx, rankType, playerID)
// rankInfo包含完整的玩家信息，但内存使用更优化
```

### 管理玩家数据缓存
```go
// 清理特定玩家的数据缓存（当玩家数据发生重大变更时）
manager.ClearPlayerDataCache(playerID)

// 系统维护时清理所有玩家数据缓存
manager.ClearAllPlayerDataCache()
```

### 监控缓存使用情况
```go
stats := manager.GetCacheStats()
// 输出示例：
// {
//   "rank_2": {"player_rank_cache_count": 100, ...},
//   "rank_3": {"player_rank_cache_count": 150, ...},
//   "global_player_data_cache": {
//     "player_data_cache_count": 200,
//     "cache_policy": "no_expiration_update_on_rank_change"
//   }
// }
```

## 性能影响

### 优势
1. **内存使用大幅减少**：特别是在多排行榜场景下
2. **数据一致性提升**：玩家基础数据只需在一处更新
3. **缓存命中率提升**：玩家基础数据缓存时间更长

### 注意事项
1. **轻微的CPU开销**：需要动态组合数据
2. **代码复杂度增加**：需要管理两套缓存系统

## 兼容性

### 向后兼容
- 所有公开API保持不变
- 缓存逻辑对外部调用者透明

### 内部变更
- CacheManager的内部实现有较大变更
- 需要更新相关的测试代码

## 最佳实践

### 1. 缓存配置建议
```go
// 玩家基础数据缓存：不自动过期，只在更新时刷新
// 无需特殊配置，系统会自动管理

// 排名数据缓存：根据排行榜类型配置
manager.SetCacheExpireTime(realtimeRankType, 60)   // 实时榜1分钟
manager.SetCacheExpireTime(periodicRankType, 300)  // 定时榜5分钟
```

### 2. 监控建议
定期检查缓存统计信息，确保内存使用在合理范围内：
```go
stats := manager.GetCacheStats()
playerDataCount := stats["global_player_data_cache"].(map[string]interface{})["player_data_cache_count"]
// 监控玩家数据缓存数量，必要时手动清理

// 当玩家数据缓存过多时，可以选择性清理
if playerDataCount.(int) > 10000 {
    // 可以实现LRU清理策略或者清理不活跃玩家的缓存
    logger.Warn("Player data cache count is high, consider cleanup")
}
```

## 总结

本次优化通过分离玩家基础数据和排名数据，并采用"不过期，只更新"的策略，实现了：
- **65%的内存节省**
- **更好的数据一致性**
- **更高的缓存效率**
- **更简单的缓存管理**

这种设计特别适合有多个排行榜的场景，玩家外观数据只存储一份，避免了重复存储和定期过期的开销，同时保持良好的性能表现。
