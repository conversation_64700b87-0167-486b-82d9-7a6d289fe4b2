package rank

import (
	"fmt"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/logger"
	"sort"
	"sync"
	"sync/atomic"
	"time"
)

// OptimizedMemoryStore 优化的内存排行榜存储
type OptimizedMemoryStore struct {
	// 使用sync.Map减少锁竞争
	rankData  sync.Map // map[int32]*MemoryRankData
	rankMutex sync.Map // map[int32]*sync.RWMutex
	
	// 玩家信息管理器
	playerInfoManager *PlayerInfoManager
	
	// 统计信息（使用原子操作）
	totalOperations int64
	cacheHits       int64
	cacheMisses     int64
}

// NewOptimizedMemoryStore 创建新的优化内存存储
func NewOptimizedMemoryStore() *OptimizedMemoryStore {
	return &OptimizedMemoryStore{
		playerInfoManager: NewPlayerInfoManager(),
	}
}

// getRankMutex 获取指定排行榜的锁（无锁优化版本）
func (oms *OptimizedMemoryStore) getRankMutex(rankType int32) *sync.RWMutex {
	// 尝试从sync.Map中获取
	if value, ok := oms.rankMutex.Load(rankType); ok {
		atomic.AddInt64(&oms.cacheHits, 1)
		return value.(*sync.RWMutex)
	}
	
	atomic.AddInt64(&oms.cacheMisses, 1)
	
	// 创建新的锁并存储
	mutex := &sync.RWMutex{}
	actual, loaded := oms.rankMutex.LoadOrStore(rankType, mutex)
	if loaded {
		// 如果已经被其他goroutine创建，使用已存在的
		return actual.(*sync.RWMutex)
	}
	
	return mutex
}

// getRankData 获取指定排行榜的数据（无锁优化版本）
func (oms *OptimizedMemoryStore) getRankData(rankType int32) *MemoryRankData {
	// 尝试从sync.Map中获取
	if value, ok := oms.rankData.Load(rankType); ok {
		atomic.AddInt64(&oms.cacheHits, 1)
		return value.(*MemoryRankData)
	}
	
	atomic.AddInt64(&oms.cacheMisses, 1)
	
	// 创建新的数据并存储
	data := NewMemoryRankData()
	actual, loaded := oms.rankData.LoadOrStore(rankType, data)
	if loaded {
		// 如果已经被其他goroutine创建，使用已存在的
		return actual.(*MemoryRankData)
	}
	
	return data
}

// UpdateEntry 更新排行榜条目（优化版本）
func (oms *OptimizedMemoryStore) UpdateEntry(rankType int32, entry *Entry, config *Config) (int32, int32, error) {
	atomic.AddInt64(&oms.totalOperations, 1)
	
	if entry == nil || entry.PlayerInfo == nil || entry.RankEntry == nil {
		return -1, -1, fmt.Errorf("invalid entry data")
	}

	// 先更新玩家基础信息到公共存储（异步优化）
	go oms.playerInfoManager.UpdatePlayerInfo(entry.PlayerInfo)

	mutex := oms.getRankMutex(rankType)
	data := oms.getRankData(rankType)

	mutex.Lock()
	defer mutex.Unlock()

	playerID := entry.PlayerInfo.PlayerID
	score := entry.RankEntry.Score

	// 检查分数是否达到上榜要求
	if score < config.MinScoreLimit {
		// 如果玩家之前在榜上，需要移除
		if _, exists := data.PlayerMap[playerID]; exists {
			oldRank := oms.findPlayerRank(data, playerID)
			oms.removeRankEntry(data, playerID)
			logger.Debug("Player removed from rank due to low score",
				zap.Uint64("playerID", playerID),
				zap.Int32("rankType", rankType),
				zap.Int32("score", score),
				zap.Int32("minLimit", config.MinScoreLimit))
			return -1, oldRank, nil
		}
		return -1, -1, nil
	}

	// 记录旧排名
	oldRank := int32(-1)
	if _, exists := data.PlayerMap[playerID]; exists {
		oldRank = oms.findPlayerRank(data, playerID)
	}

	// 更新或添加排行榜条目（只存储排行榜相关数据）
	rankEntry := &RankEntry{
		PlayerID:   playerID,
		Score:      score,
		UpdateTime: time.Now().Unix(),
	}
	data.PlayerMap[playerID] = rankEntry

	// 智能重建排序列表（只在必要时重建）
	oms.smartRebuildSortedList(data, config, oldRank != -1)

	// 查找新排名
	newRank := oms.findPlayerRank(data, playerID)

	logger.Debug("Player rank updated",
		zap.Uint64("playerID", playerID),
		zap.Int32("rankType", rankType),
		zap.Int32("oldRank", oldRank),
		zap.Int32("newRank", newRank),
		zap.Int32("score", score))

	return newRank, oldRank, nil
}

// smartRebuildSortedList 智能重建排序列表（减少不必要的排序）
func (oms *OptimizedMemoryStore) smartRebuildSortedList(data *MemoryRankData, config *Config, isUpdate bool) {
	// 如果是更新操作且排行榜较小，可以考虑插入排序
	if isUpdate && len(data.PlayerMap) < 100 {
		// 对于小规模排行榜，使用插入排序可能更高效
		oms.insertionSortUpdate(data, config)
		return
	}
	
	// 对于大规模排行榜或新增操作，使用完整重建
	oms.rebuildSortedList(data, config)
}

// insertionSortUpdate 使用插入排序更新（适用于小规模排行榜）
func (oms *OptimizedMemoryStore) insertionSortUpdate(data *MemoryRankData, config *Config) {
	// 将所有条目放入切片
	entries := make([]*RankEntry, 0, len(data.PlayerMap))
	for _, entry := range data.PlayerMap {
		entries = append(entries, entry)
	}

	// 使用插入排序（对于小数据集更高效）
	for i := 1; i < len(entries); i++ {
		key := entries[i]
		j := i - 1
		
		for j >= 0 && oms.compareEntries(entries[j], key) {
			entries[j+1] = entries[j]
			j--
		}
		entries[j+1] = key
	}

	// 限制排行榜大小和更新排名
	oms.finalizeEntries(data, entries, config)
}

// compareEntries 比较两个条目（用于排序）
func (oms *OptimizedMemoryStore) compareEntries(a, b *RankEntry) bool {
	if a.Score == b.Score {
		// 分数相同时，按更新时间升序排序（先更新的排名靠前）
		return a.UpdateTime > b.UpdateTime
	}
	return a.Score < b.Score
}

// rebuildSortedList 重新构建排序列表（完整版本）
func (oms *OptimizedMemoryStore) rebuildSortedList(data *MemoryRankData, config *Config) {
	// 将所有条目放入切片
	entries := make([]*RankEntry, 0, len(data.PlayerMap))
	for _, entry := range data.PlayerMap {
		entries = append(entries, entry)
	}

	// 按分数降序排序
	sort.Slice(entries, func(i, j int) bool {
		if entries[i].Score == entries[j].Score {
			// 分数相同时，按更新时间升序排序（先更新的排名靠前）
			return entries[i].UpdateTime < entries[j].UpdateTime
		}
		return entries[i].Score > entries[j].Score
	})

	// 限制排行榜大小和更新排名
	oms.finalizeEntries(data, entries, config)
}

// finalizeEntries 完成条目处理（限制大小和更新排名）
func (oms *OptimizedMemoryStore) finalizeEntries(data *MemoryRankData, entries []*RankEntry, config *Config) {
	// 限制排行榜大小
	if int32(len(entries)) > config.MaxRankLimit {
		// 移除超出限制的条目
		for i := config.MaxRankLimit; i < int32(len(entries)); i++ {
			delete(data.PlayerMap, entries[i].PlayerID)
		}
		entries = entries[:config.MaxRankLimit]
	}

	// 更新排名
	for i, entry := range entries {
		entry.Ranking = int32(i + 1)
	}

	data.Entries = entries
	data.TotalCount = int64(len(entries))
	data.UpdateTime = time.Now().Unix()
}

// removeRankEntry 移除排行榜条目
func (oms *OptimizedMemoryStore) removeRankEntry(data *MemoryRankData, playerID uint64) {
	delete(data.PlayerMap, playerID)

	// 从排序列表中移除
	for i, entry := range data.Entries {
		if entry.PlayerID == playerID {
			data.Entries = append(data.Entries[:i], data.Entries[i+1:]...)
			break
		}
	}

	data.TotalCount = int64(len(data.Entries))
	data.UpdateTime = time.Now().Unix()
}

// findPlayerRank 查找玩家排名
func (oms *OptimizedMemoryStore) findPlayerRank(data *MemoryRankData, playerID uint64) int32 {
	for i, entry := range data.Entries {
		if entry.PlayerID == playerID {
			return int32(i + 1)
		}
	}
	return -1
}

// GetStats 获取优化存储的统计信息
func (oms *OptimizedMemoryStore) GetStats() (int64, int64, int64) {
	return atomic.LoadInt64(&oms.totalOperations),
		   atomic.LoadInt64(&oms.cacheHits),
		   atomic.LoadInt64(&oms.cacheMisses)
}
