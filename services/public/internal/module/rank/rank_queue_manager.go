package rank

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/logger"
	"sync"
)

// QueueManager 队列管理器
type QueueManager struct {
	// 每个排行榜的更新队列
	updateQueues map[int32]*UpdateQueue
	// 内存存储
	memoryStore *MemoryStore
	// 排行榜配置
	configs map[int32]*Config
	// 互斥锁
	mutex sync.RWMutex
	// 是否已启动
	started bool
}

// NewQueueManager 创建新的队列管理器
func NewQueueManager() *QueueManager {
	return &QueueManager{
		updateQueues: make(map[int32]*UpdateQueue),
		memoryStore:  NewMemoryStore(),
		configs:      make(map[int32]*Config),
		started:      false,
	}
}

// initializeMemoryStore 初始化内存存储
func (qm *QueueManager) initializeMemoryStore() {
	if len(qm.configs) == 0 {
		logger.Warn("No rank configs found, skipping memory store initialization")
		return
	}

	// 收集所有排行榜类型
	rankTypes := make([]int32, 0, len(qm.configs))
	for rankType := range qm.configs {
		rankTypes = append(rankTypes, rankType)
	}

	// 初始化内存存储
	qm.memoryStore.InitializeRankTypes(rankTypes)
	logger.Info("Memory store initialized with rank types", zap.Int("count", len(rankTypes)))
}

// AddRankConfig 添加排行榜配置
func (qm *QueueManager) AddRankConfig(config *Config) error {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	if qm.started {
		return fmt.Errorf("cannot add config after queue manager started")
	}

	// 设置默认值
	if config.QueueSize <= 0 {
		config.QueueSize = 1000
	}
	if config.WorkerCount <= 0 {
		config.WorkerCount = 2
	}
	if config.BatchSize <= 0 {
		config.BatchSize = 10
	}

	qm.configs[config.RankType] = config

	// 创建对应的更新队列
	updateQueue := NewUpdateQueue(config.RankType, config, qm.memoryStore)
	qm.updateQueues[config.RankType] = updateQueue

	logger.Info("Added rank config",
		zap.Int32("rankType", config.RankType),
		zap.Int32("queueSize", config.QueueSize),
		zap.Int32("workerCount", config.WorkerCount),
		zap.Int32("batchSize", config.BatchSize))

	return nil
}

// Start 启动队列管理器
func (qm *QueueManager) Start() error {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	if qm.started {
		return fmt.Errorf("queue manager already started")
	}

	logger.Info("Starting queue manager", zap.Int("rankCount", len(qm.updateQueues)))

	// 初始化内存存储（提前创建所有rankData和rankMutex）
	qm.initializeMemoryStore()

	// 启动所有更新队列，一个排行榜一个更新队列
	for rankType, queue := range qm.updateQueues {
		queue.Start()
		logger.Info("Started update queue", zap.Int32("rankType", rankType))
	}

	qm.started = true
	logger.Info("Queue manager started successfully")
	return nil
}

// Stop 停止队列管理器
func (qm *QueueManager) Stop() {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	if !qm.started {
		return
	}

	logger.Info("Stopping queue manager")

	// 停止所有更新队列
	for rankType, queue := range qm.updateQueues {
		queue.Stop()
		logger.Info("Stopped update queue", zap.Int32("rankType", rankType))
	}

	qm.started = false
	logger.Info("Queue manager stopped")
}

// UpdateRank 更新排行榜
func (qm *QueueManager) UpdateRank(ctx context.Context, rankType int32, entry *Entry) error {
	qm.mutex.RLock()
	queue, exists := qm.updateQueues[rankType]
	qm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("rank type %d not found", rankType)
	}

	request := &UpdateRequest{
		RankType: rankType,
		Entry:    entry,
		Priority: 0, // 默认优先级
	}

	success := queue.AddUpdateRequest(request)
	if !success {
		return fmt.Errorf("failed to add update request to queue, queue may be full")
	}

	return nil
}

// GetRankList 获取排行榜列表
func (qm *QueueManager) GetRankList(ctx context.Context, rankType int32, start, count int32) ([]*Entry, int64, error) {
	qm.mutex.RLock()
	config, exists := qm.configs[rankType]
	qm.mutex.RUnlock()

	if !exists {
		return nil, 0, fmt.Errorf("rank type %d not found", rankType)
	}

	// 检查请求参数
	if count > config.MaxQueryLimit {
		count = config.MaxQueryLimit
	}

	return qm.memoryStore.GetRankList(rankType, start, count)
}

// GetPlayerRank 获取玩家排名
func (qm *QueueManager) GetPlayerRank(ctx context.Context, rankType int32, playerID uint64) (int32, *Entry, error) {
	qm.mutex.RLock()
	_, exists := qm.configs[rankType]
	qm.mutex.RUnlock()

	if !exists {
		return -1, nil, fmt.Errorf("rank type %d not found", rankType)
	}

	return qm.memoryStore.GetPlayerRank(rankType, playerID)
}

// ClearRank 清空排行榜
func (qm *QueueManager) ClearRank(ctx context.Context, rankType int32) error {
	qm.mutex.RLock()
	_, exists := qm.configs[rankType]
	qm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("rank type %d not found", rankType)
	}

	return qm.memoryStore.ClearRank(rankType)
}

// GetQueueStats 获取队列统计信息
func (qm *QueueManager) GetQueueStats(rankType int32) (QueueStats, error) {
	qm.mutex.RLock()
	queue, exists := qm.updateQueues[rankType]
	qm.mutex.RUnlock()

	if !exists {
		return QueueStats{}, fmt.Errorf("rank type %d not found", rankType)
	}

	return queue.GetStats(), nil
}

// GetAllQueueStats 获取所有队列统计信息
func (qm *QueueManager) GetAllQueueStats() map[int32]QueueStats {
	qm.mutex.RLock()
	defer qm.mutex.RUnlock()

	stats := make(map[int32]QueueStats)
	for rankType, queue := range qm.updateQueues {
		stats[rankType] = queue.GetStats()
	}

	return stats
}

// GetRankStats 获取排行榜统计信息
func (qm *QueueManager) GetRankStats(rankType int32) (int64, int64, error) {
	qm.mutex.RLock()
	_, exists := qm.configs[rankType]
	qm.mutex.RUnlock()

	if !exists {
		return 0, 0, fmt.Errorf("rank type %d not found", rankType)
	}

	totalCount, updateTime := qm.memoryStore.GetStats(rankType)
	return totalCount, updateTime, nil
}

// IsQueueFull 检查队列是否已满
func (qm *QueueManager) IsQueueFull(rankType int32) bool {
	qm.mutex.RLock()
	queue, exists := qm.updateQueues[rankType]
	qm.mutex.RUnlock()

	if !exists {
		return false
	}

	return queue.IsQueueFull()
}

// GetQueueLength 获取队列长度
func (qm *QueueManager) GetQueueLength(rankType int32) int32 {
	qm.mutex.RLock()
	queue, exists := qm.updateQueues[rankType]
	qm.mutex.RUnlock()

	if !exists {
		return 0
	}

	return queue.GetQueueLength()
}
