package rank

import (
	"go.uber.org/zap"
	"hash/fnv"
	"kairo_paradise_server/internal/logger"
	"sync"
	"sync/atomic"
	"time"
)

// OptimizedPlayerInfoManager 优化的玩家信息管理器（使用分片锁）
type OptimizedPlayerInfoManager struct {
	// 玩家信息分片
	shards []*PlayerInfoShard
	// 分片数量
	shardCount int32
	// 统计信息（使用原子操作）
	totalPlayers int64
	updateCount  int64
}

// PlayerInfoShard 玩家信息分片
type PlayerInfoShard struct {
	playerInfoMap map[uint64]*PlayerInfo
	mutex         sync.RWMutex
}

// NewOptimizedPlayerInfoManager 创建优化的玩家信息管理器
func NewOptimizedPlayerInfoManager() *OptimizedPlayerInfoManager {
	return NewOptimizedPlayerInfoManagerWithShards(16) // 默认16个分片
}

// NewOptimizedPlayerInfoManagerWithShards 创建指定分片数的玩家信息管理器
func NewOptimizedPlayerInfoManagerWithShards(shardCount int32) *OptimizedPlayerInfoManager {
	if shardCount <= 0 {
		shardCount = 16
	}

	shards := make([]*PlayerInfoShard, shardCount)
	for i := int32(0); i < shardCount; i++ {
		shards[i] = &PlayerInfoShard{
			playerInfoMap: make(map[uint64]*PlayerInfo),
		}
	}

	return &OptimizedPlayerInfoManager{
		shards:       shards,
		shardCount:   shardCount,
		totalPlayers: 0,
		updateCount:  0,
	}
}

// getShard 根据玩家ID获取对应的分片
func (opium *OptimizedPlayerInfoManager) getShard(playerID uint64) *PlayerInfoShard {
	hash := fnv.New32a()
	// 将uint64转换为字节数组进行哈希
	hash.Write([]byte{
		byte(playerID), byte(playerID >> 8), byte(playerID >> 16), byte(playerID >> 24),
		byte(playerID >> 32), byte(playerID >> 40), byte(playerID >> 48), byte(playerID >> 56),
	})
	shardIndex := hash.Sum32() % uint32(opium.shardCount)
	return opium.shards[shardIndex]
}

// UpdatePlayerInfo 更新玩家基础信息（优化版）
func (opium *OptimizedPlayerInfoManager) UpdatePlayerInfo(playerInfo *PlayerInfo) {
	if playerInfo == nil || playerInfo.PlayerID == 0 {
		logger.Warn("Invalid player info", zap.Uint64("playerID", playerInfo.PlayerID))
		return
	}

	shard := opium.getShard(playerInfo.PlayerID)
	shard.mutex.Lock()
	defer shard.mutex.Unlock()

	// 检查是否是新玩家
	if _, exists := shard.playerInfoMap[playerInfo.PlayerID]; !exists {
		atomic.AddInt64(&opium.totalPlayers, 1)
	}

	// 更新时间戳
	playerInfo.UpdateTime = time.Now().Unix()

	// 存储玩家信息
	shard.playerInfoMap[playerInfo.PlayerID] = playerInfo
	atomic.AddInt64(&opium.updateCount, 1)
}

// GetPlayerInfo 获取玩家基础信息（优化版）
func (opium *OptimizedPlayerInfoManager) GetPlayerInfo(playerID uint64) *PlayerInfo {
	shard := opium.getShard(playerID)
	shard.mutex.RLock()
	defer shard.mutex.RUnlock()

	playerInfo, exists := shard.playerInfoMap[playerID]
	if !exists {
		return nil
	}

	// 返回副本以避免外部修改
	return &PlayerInfo{
		PlayerID:   playerInfo.PlayerID,
		PlayerName: playerInfo.PlayerName,
		Level:      playerInfo.Level,
		Icon:       playerInfo.Icon,
		UpdateTime: playerInfo.UpdateTime,
		Prosperity: playerInfo.Prosperity,
	}
}

// GetPlayerInfoBatch 批量获取玩家基础信息（优化版，减少锁竞争）
func (opium *OptimizedPlayerInfoManager) GetPlayerInfoBatch(playerIDs []uint64) map[uint64]*PlayerInfo {
	if len(playerIDs) == 0 {
		return make(map[uint64]*PlayerInfo)
	}

	// 按分片分组玩家ID
	shardGroups := make(map[int32][]uint64)
	for _, playerID := range playerIDs {
		hash := fnv.New32a()
		hash.Write([]byte{
			byte(playerID), byte(playerID >> 8), byte(playerID >> 16), byte(playerID >> 24),
			byte(playerID >> 32), byte(playerID >> 40), byte(playerID >> 48), byte(playerID >> 56),
		})
		shardIndex := int32(hash.Sum32() % uint32(opium.shardCount))

		if shardGroups[shardIndex] == nil {
			shardGroups[shardIndex] = make([]uint64, 0)
		}
		shardGroups[shardIndex] = append(shardGroups[shardIndex], playerID)
	}

	// 并行处理各分片
	result := make(map[uint64]*PlayerInfo)
	var resultMutex sync.Mutex
	var wg sync.WaitGroup

	for shardIndex, playerIDsInShard := range shardGroups {
		wg.Add(1)
		go func(sIdx int32, pIDs []uint64) {
			defer wg.Done()

			shard := opium.shards[sIdx]
			shard.mutex.RLock()
			defer shard.mutex.RUnlock()

			// 在分片内批量处理
			localResult := make(map[uint64]*PlayerInfo)
			for _, playerID := range pIDs {
				if playerInfo, exists := shard.playerInfoMap[playerID]; exists {
					// 返回副本以避免外部修改
					localResult[playerID] = &PlayerInfo{
						PlayerID:   playerInfo.PlayerID,
						PlayerName: playerInfo.PlayerName,
						Level:      playerInfo.Level,
						Icon:       playerInfo.Icon,
						UpdateTime: playerInfo.UpdateTime,
						Prosperity: playerInfo.Prosperity,
					}
				}
			}

			// 合并结果
			resultMutex.Lock()
			for playerID, playerInfo := range localResult {
				result[playerID] = playerInfo
			}
			resultMutex.Unlock()
		}(shardIndex, playerIDsInShard)
	}

	wg.Wait()
	return result
}

// HasPlayerInfo 检查玩家信息是否存在（优化版）
func (opium *OptimizedPlayerInfoManager) HasPlayerInfo(playerID uint64) bool {
	shard := opium.getShard(playerID)
	shard.mutex.RLock()
	defer shard.mutex.RUnlock()

	_, exists := shard.playerInfoMap[playerID]
	return exists
}

// RemovePlayerInfo 移除玩家信息（优化版）
func (opium *OptimizedPlayerInfoManager) RemovePlayerInfo(playerID uint64) bool {
	shard := opium.getShard(playerID)
	shard.mutex.Lock()
	defer shard.mutex.Unlock()

	if _, exists := shard.playerInfoMap[playerID]; exists {
		delete(shard.playerInfoMap, playerID)
		atomic.AddInt64(&opium.totalPlayers, -1)

		logger.Debug("Player info removed", zap.Uint64("playerID", playerID))
		return true
	}

	return false
}

// ClearAll 清空所有玩家信息（优化版）
func (opium *OptimizedPlayerInfoManager) ClearAll() {
	// 并行清理所有分片
	var wg sync.WaitGroup
	for _, shard := range opium.shards {
		wg.Add(1)
		go func(s *PlayerInfoShard) {
			defer wg.Done()
			s.mutex.Lock()
			defer s.mutex.Unlock()
			s.playerInfoMap = make(map[uint64]*PlayerInfo)
		}(shard)
	}
	wg.Wait()

	// 重置统计信息
	atomic.StoreInt64(&opium.totalPlayers, 0)
	atomic.StoreInt64(&opium.updateCount, 0)

	logger.Info("All player info cleared")
}

// GetAllPlayerIDs 获取所有玩家ID（优化版）
func (opium *OptimizedPlayerInfoManager) GetAllPlayerIDs() []uint64 {
	// 并行收集所有分片的玩家ID
	var wg sync.WaitGroup
	var resultMutex sync.Mutex
	playerIDs := make([]uint64, 0)

	for _, shard := range opium.shards {
		wg.Add(1)
		go func(s *PlayerInfoShard) {
			defer wg.Done()

			s.mutex.RLock()
			defer s.mutex.RUnlock()

			localIDs := make([]uint64, 0, len(s.playerInfoMap))
			for playerID := range s.playerInfoMap {
				localIDs = append(localIDs, playerID)
			}

			resultMutex.Lock()
			playerIDs = append(playerIDs, localIDs...)
			resultMutex.Unlock()
		}(shard)
	}

	wg.Wait()
	return playerIDs
}

// GetPlayerCount 获取玩家总数（无锁版本）
func (opium *OptimizedPlayerInfoManager) GetPlayerCount() int64 {
	return atomic.LoadInt64(&opium.totalPlayers)
}

// GetUpdateCount 获取更新次数（无锁版本）
func (opium *OptimizedPlayerInfoManager) GetUpdateCount() int64 {
	return atomic.LoadInt64(&opium.updateCount)
}

// CleanupOldPlayers 清理长时间未更新的玩家信息（优化版）
func (opium *OptimizedPlayerInfoManager) CleanupOldPlayers(maxAge int64) int {
	currentTime := time.Now().Unix()
	totalCleanupCount := int64(0)

	// 并行清理各分片
	var wg sync.WaitGroup
	for _, shard := range opium.shards {
		wg.Add(1)
		go func(s *PlayerInfoShard) {
			defer wg.Done()

			s.mutex.Lock()
			defer s.mutex.Unlock()

			localCleanupCount := int64(0)
			for playerID, playerInfo := range s.playerInfoMap {
				if currentTime-playerInfo.UpdateTime > maxAge {
					delete(s.playerInfoMap, playerID)
					localCleanupCount++
				}
			}

			if localCleanupCount > 0 {
				atomic.AddInt64(&opium.totalPlayers, -localCleanupCount)
				atomic.AddInt64(&totalCleanupCount, localCleanupCount)
			}
		}(shard)
	}

	wg.Wait()

	cleanupCount := int(atomic.LoadInt64(&totalCleanupCount))
	if cleanupCount > 0 {
		logger.Info("Cleaned up old player info",
			zap.Int("cleanupCount", cleanupCount),
			zap.Int64("maxAge", maxAge))
	}

	return cleanupCount
}

// GetOptimizationStats 获取优化统计信息
func (opium *OptimizedPlayerInfoManager) GetOptimizationStats() map[string]interface{} {
	// 统计各分片的负载情况
	shardLoads := make([]int, opium.shardCount)
	for i, shard := range opium.shards {
		shard.mutex.RLock()
		shardLoads[i] = len(shard.playerInfoMap)
		shard.mutex.RUnlock()
	}

	return map[string]interface{}{
		"shard_count":   opium.shardCount,
		"total_players": atomic.LoadInt64(&opium.totalPlayers),
		"update_count":  atomic.LoadInt64(&opium.updateCount),
		"shard_loads":   shardLoads,
	}
}
