package rank

import (
	"fmt"
	"sync"
	"testing"
)

// 简化的日志接口，用于测试
type testLogger struct{}

func (l *testLogger) Info(msg string, fields ...interface{}) {
	fmt.Printf("[INFO] %s\n", msg)
}

func (l *testLogger) Debug(msg string, fields ...interface{}) {
	fmt.Printf("[DEBUG] %s\n", msg)
}

func (l *testLogger) Error(msg string, fields ...interface{}) {
	fmt.Printf("[ERROR] %s\n", msg)
}

func (l *testLogger) Warn(msg string, fields ...interface{}) {
	fmt.Printf("[WARN] %s\n", msg)
}

// 独立的内存存储测试
func TestStandaloneMemoryStore(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	// 测试更新条目
	entry1 := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 1500,
	}

	newRank, oldRank, err := store.UpdateEntry(2, entry1, config)
	if err != nil {
		t.Fatalf("Failed to update entry: %v", err)
	}

	if newRank != 1 {
		t.Errorf("Expected rank 1, got %d", newRank)
	}

	if oldRank != -1 {
		t.Errorf("Expected old rank -1, got %d", oldRank)
	}

	// 测试获取排行榜列表
	entries, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if len(entries) != 1 {
		t.Errorf("Expected 1 entry, got %d", len(entries))
	}

	if totalCount != 1 {
		t.Errorf("Expected total count 1, got %d", totalCount)
	}

	// 测试获取玩家排名
	rank, entry, err := store.GetPlayerRank(2, 1001)
	if err != nil {
		t.Fatalf("Failed to get player rank: %v", err)
	}

	if rank != 1 {
		t.Errorf("Expected rank 1, got %d", rank)
	}

	if entry.PlayerID != 1001 {
		t.Errorf("Expected player ID 1001, got %d", entry.PlayerID)
	}
}

// 测试多个条目的排序
func TestStandaloneMemoryStoreOrdering(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	// 添加多个条目
	entries := []*Entry{
		{PlayerID: 1001, PlayerName: "Player1", Level: 10, Prosperity: 1500},
		{PlayerID: 1002, PlayerName: "Player2", Level: 15, Prosperity: 2000},
		{PlayerID: 1003, PlayerName: "Player3", Level: 12, Prosperity: 1800},
		{PlayerID: 1004, PlayerName: "Player4", Level: 8, Prosperity: 1200},
		{PlayerID: 1005, PlayerName: "Player5", Level: 20, Prosperity: 2500},
	}

	for _, entry := range entries {
		_, _, err := store.UpdateEntry(2, entry, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", entry.PlayerID, err)
		}
	}

	// 检查排序是否正确（按繁荣度降序）
	rankList, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if totalCount != 5 {
		t.Errorf("Expected total count 5, got %d", totalCount)
	}

	if len(rankList) != 5 {
		t.Errorf("Expected 5 entries, got %d", len(rankList))
	}

	// 验证排序：Player5(2500) > Player2(2000) > Player3(1800) > Player1(1500) > Player4(1200)
	expectedOrder := []uint64{1005, 1002, 1003, 1001, 1004}
	expectedScores := []int32{2500, 2000, 1800, 1500, 1200}

	for i, entry := range rankList {
		if entry.PlayerID != expectedOrder[i] {
			t.Errorf("Expected player %d at rank %d, got player %d", expectedOrder[i], i+1, entry.PlayerID)
		}
		if entry.Prosperity != expectedScores[i] {
			t.Errorf("Expected score %d for player %d, got %d", expectedScores[i], entry.PlayerID, entry.Prosperity)
		}
		if entry.Ranking != int32(i+1) {
			t.Errorf("Expected ranking %d for player %d, got %d", i+1, entry.PlayerID, entry.Ranking)
		}
	}
}

// 测试最低分数限制
func TestStandaloneMinScoreLimit(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 1000, // 设置最低分数限制
		MaxRankLimit:  1000,
	}

	// 测试低于最低分数的条目
	lowScoreEntry := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 500, // 低于最低分数
	}

	newRank, oldRank, err := store.UpdateEntry(2, lowScoreEntry, config)
	if err != nil {
		t.Fatalf("Failed to update entry: %v", err)
	}

	if newRank != -1 {
		t.Errorf("Expected rank -1 for low score, got %d", newRank)
	}

	if oldRank != -1 {
		t.Errorf("Expected old rank -1, got %d", oldRank)
	}

	// 验证排行榜为空
	entries, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if len(entries) != 0 {
		t.Errorf("Expected 0 entries, got %d", len(entries))
	}

	if totalCount != 0 {
		t.Errorf("Expected total count 0, got %d", totalCount)
	}
}

// 测试并发安全性
func TestStandaloneConcurrency(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	const numGoroutines = 10
	const numUpdatesPerGoroutine = 100

	var wg sync.WaitGroup

	// 并发更新
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < numUpdatesPerGoroutine; j++ {
				playerID := uint64(goroutineID*1000 + j)
				entry := &Entry{
					PlayerID:   playerID,
					PlayerName: fmt.Sprintf("Player%d", playerID),
					Level:      int32(10 + j%20),
					Prosperity: int32(1000 + j*10),
				}

				_, _, err := store.UpdateEntry(2, entry, config)
				if err != nil {
					t.Errorf("Failed to update entry for player %d: %v", playerID, err)
				}
			}
		}(i)
	}

	// 并发查询
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for j := 0; j < numUpdatesPerGoroutine; j++ {
				_, _, err := store.GetRankList(2, 1, 50)
				if err != nil {
					t.Errorf("Failed to get rank list: %v", err)
				}

				// 随机查询玩家排名
				playerID := uint64(j % 100)
				_, _, err = store.GetPlayerRank(2, playerID)
				if err != nil {
					t.Errorf("Failed to get player rank: %v", err)
				}
			}
		}()
	}

	wg.Wait()

	// 验证最终状态
	entries, totalCount, err := store.GetRankList(2, 1, 100)
	if err != nil {
		t.Fatalf("Failed to get final rank list: %v", err)
	}

	if totalCount == 0 {
		t.Error("Expected some entries after concurrent updates")
	}

	// 验证排序正确性
	for i := 1; i < len(entries); i++ {
		if entries[i-1].Prosperity < entries[i].Prosperity {
			t.Errorf("Entries not properly sorted: entry %d has score %d, entry %d has score %d",
				i-1, entries[i-1].Prosperity, i, entries[i].Prosperity)
		}
	}
}

// 性能基准测试
func BenchmarkStandaloneMemoryStoreUpdate(b *testing.B) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: fmt.Sprintf("Player%d", i),
			Level:      int32(10 + i%50),
			Prosperity: int32(1000 + i*10),
		}

		_, _, err := store.UpdateEntry(2, entry, config)
		if err != nil {
			b.Fatalf("Failed to update entry: %v", err)
		}
	}
}

func BenchmarkStandaloneMemoryStoreQuery(b *testing.B) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
	}

	// 预填充数据
	for i := 0; i < 1000; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: fmt.Sprintf("Player%d", i),
			Level:      int32(10 + i%50),
			Prosperity: int32(1000 + i*10),
		}

		store.UpdateEntry(2, entry, config)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _, err := store.GetRankList(2, 1, 50)
		if err != nil {
			b.Fatalf("Failed to get rank list: %v", err)
		}
	}
}

// 测试排行榜更新和移除
func TestStandaloneRankUpdateAndRemoval(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 1000,
		MaxRankLimit:  5, // 限制最多5名
	}

	// 添加6个玩家，但只有5个能上榜
	players := []*Entry{
		{PlayerID: 1001, PlayerName: "Player1", Level: 10, Prosperity: 1500},
		{PlayerID: 1002, PlayerName: "Player2", Level: 15, Prosperity: 2000},
		{PlayerID: 1003, PlayerName: "Player3", Level: 12, Prosperity: 1800},
		{PlayerID: 1004, PlayerName: "Player4", Level: 8, Prosperity: 1200},
		{PlayerID: 1005, PlayerName: "Player5", Level: 20, Prosperity: 2500},
		{PlayerID: 1006, PlayerName: "Player6", Level: 5, Prosperity: 1100}, // 应该被排除
	}

	for _, player := range players {
		store.UpdateEntry(2, player, config)
	}

	// 验证只有5个玩家上榜
	entries, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if totalCount != 5 {
		t.Errorf("Expected total count 5, got %d", totalCount)
	}

	if len(entries) != 5 {
		t.Errorf("Expected 5 entries, got %d", len(entries))
	}

	// 验证Player6不在榜上
	rank, _, err := store.GetPlayerRank(2, 1006)
	if err != nil {
		t.Fatalf("Failed to get player rank: %v", err)
	}

	if rank != -1 {
		t.Errorf("Expected Player6 not in rank (-1), got %d", rank)
	}

	// 测试玩家分数降低后被移除
	lowScorePlayer := &Entry{
		PlayerID:   1002, // Player2
		PlayerName: "Player2",
		Level:      15,
		Prosperity: 500, // 低于最低分数
	}

	newRank, oldRank, err := store.UpdateEntry(2, lowScorePlayer, config)
	if err != nil {
		t.Fatalf("Failed to update entry: %v", err)
	}

	if newRank != -1 {
		t.Errorf("Expected Player2 to be removed (-1), got rank %d", newRank)
	}

	if oldRank == -1 {
		t.Errorf("Expected Player2 to have previous rank, got %d", oldRank)
	}

	// 验证Player2已被移除
	entries, totalCount, err = store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if totalCount != 4 {
		t.Errorf("Expected total count 4 after removal, got %d", totalCount)
	}

	// 验证Player2不在榜上
	rank, _, err = store.GetPlayerRank(2, 1002)
	if err != nil {
		t.Fatalf("Failed to get player rank: %v", err)
	}

	if rank != -1 {
		t.Errorf("Expected Player2 not in rank (-1), got %d", rank)
	}
}
