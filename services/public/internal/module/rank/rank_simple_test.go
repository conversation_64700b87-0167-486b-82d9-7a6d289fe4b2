package rank

import (
	"testing"
	"time"
)

func TestMemoryStoreBasic(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}
	
	// 测试更新条目
	entry1 := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 1500,
	}
	
	newRank, oldRank, err := store.UpdateEntry(2, entry1, config)
	if err != nil {
		t.Fatalf("Failed to update entry: %v", err)
	}
	
	if newRank != 1 {
		t.<PERSON><PERSON><PERSON>("Expected rank 1, got %d", newRank)
	}
	
	if oldRank != -1 {
		t.<PERSON><PERSON><PERSON>("Expected old rank -1, got %d", oldRank)
	}
	
	// 测试获取排行榜列表
	entries, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}
	
	if len(entries) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 entry, got %d", len(entries))
	}
	
	if totalCount != 1 {
		t.<PERSON><PERSON><PERSON>("Expected total count 1, got %d", totalCount)
	}
	
	// 测试获取玩家排名
	rank, entry, err := store.GetPlayerRank(2, 1001)
	if err != nil {
		t.Fatalf("Failed to get player rank: %v", err)
	}
	
	if rank != 1 {
		t.Errorf("Expected rank 1, got %d", rank)
	}
	
	if entry.PlayerID != 1001 {
		t.Errorf("Expected player ID 1001, got %d", entry.PlayerID)
	}
}

func TestMemoryStoreMultipleEntries(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}
	
	// 添加多个条目
	entries := []*Entry{
		{PlayerID: 1001, PlayerName: "Player1", Level: 10, Prosperity: 1500},
		{PlayerID: 1002, PlayerName: "Player2", Level: 15, Prosperity: 2000},
		{PlayerID: 1003, PlayerName: "Player3", Level: 12, Prosperity: 1800},
		{PlayerID: 1004, PlayerName: "Player4", Level: 8, Prosperity: 1200},
		{PlayerID: 1005, PlayerName: "Player5", Level: 20, Prosperity: 2500},
	}
	
	for _, entry := range entries {
		_, _, err := store.UpdateEntry(2, entry, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", entry.PlayerID, err)
		}
	}
	
	// 检查排序是否正确（按繁荣度降序）
	rankList, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}
	
	if totalCount != 5 {
		t.Errorf("Expected total count 5, got %d", totalCount)
	}
	
	if len(rankList) != 5 {
		t.Errorf("Expected 5 entries, got %d", len(rankList))
	}
	
	// 验证排序：Player5(2500) > Player2(2000) > Player3(1800) > Player1(1500) > Player4(1200)
	expectedOrder := []uint64{1005, 1002, 1003, 1001, 1004}
	for i, entry := range rankList {
		if entry.PlayerID != expectedOrder[i] {
			t.Errorf("Expected player %d at rank %d, got player %d", expectedOrder[i], i+1, entry.PlayerID)
		}
		if entry.Ranking != int32(i+1) {
			t.Errorf("Expected ranking %d for player %d, got %d", i+1, entry.PlayerID, entry.Ranking)
		}
	}
}

func TestMemoryStoreMinScoreLimit(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 1000, // 设置最低分数限制
		MaxRankLimit:  1000,
	}
	
	// 测试低于最低分数的条目
	lowScoreEntry := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 500, // 低于最低分数
	}
	
	newRank, oldRank, err := store.UpdateEntry(2, lowScoreEntry, config)
	if err != nil {
		t.Fatalf("Failed to update entry: %v", err)
	}
	
	if newRank != -1 {
		t.Errorf("Expected rank -1 for low score, got %d", newRank)
	}
	
	if oldRank != -1 {
		t.Errorf("Expected old rank -1, got %d", oldRank)
	}
	
	// 验证排行榜为空
	entries, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}
	
	if len(entries) != 0 {
		t.Errorf("Expected 0 entries, got %d", len(entries))
	}
	
	if totalCount != 0 {
		t.Errorf("Expected total count 0, got %d", totalCount)
	}
}

func TestUpdateQueueBasic(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
		QueueSize:     100,
		WorkerCount:   2,
		BatchSize:     5,
	}
	
	queue := NewUpdateQueue(2, config, store)
	queue.Start()
	defer queue.Stop()
	
	// 添加更新请求
	entry := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 1500,
	}
	
	request := &UpdateRequest{
		RankType: 2,
		Entry:    entry,
		Priority: 0,
	}
	
	success := queue.AddUpdateRequest(request)
	if !success {
		t.Fatal("Failed to add update request")
	}
	
	// 等待处理完成
	time.Sleep(1 * time.Second)
	
	// 检查统计
	stats := queue.GetStats()
	if stats.ProcessedCount == 0 {
		t.Error("Expected processed count > 0")
	}
	
	// 验证数据已更新到内存存储
	rank, resultEntry, err := store.GetPlayerRank(2, 1001)
	if err != nil {
		t.Fatalf("Failed to get player rank: %v", err)
	}
	
	if rank != 1 {
		t.Errorf("Expected rank 1, got %d", rank)
	}
	
	if resultEntry.PlayerID != 1001 {
		t.Errorf("Expected player ID 1001, got %d", resultEntry.PlayerID)
	}
}

func TestQueueManagerBasic(t *testing.T) {
	manager := NewQueueManager()
	
	// 添加配置
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxQueryLimit: 500,
		ShowRankLimit: 300,
		MaxRankLimit:  1000,
		QueueSize:     100,
		WorkerCount:   2,
		BatchSize:     5,
	}
	
	err := manager.AddRankConfig(config)
	if err != nil {
		t.Fatalf("Failed to add rank config: %v", err)
	}
	
	// 启动管理器
	err = manager.Start()
	if err != nil {
		t.Fatalf("Failed to start queue manager: %v", err)
	}
	defer manager.Stop()
	
	// 等待启动完成
	time.Sleep(100 * time.Millisecond)
	
	// 检查队列统计
	stats, err := manager.GetQueueStats(2)
	if err != nil {
		t.Fatalf("Failed to get queue stats: %v", err)
	}
	
	if stats.WorkerCount != 2 {
		t.Errorf("Expected worker count 2, got %d", stats.WorkerCount)
	}
}

func BenchmarkMemoryStoreUpdateSimple(b *testing.B) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: "Player",
			Level:      10,
			Prosperity: int32(1000 + i),
		}
		
		_, _, err := store.UpdateEntry(2, entry, config)
		if err != nil {
			b.Fatalf("Failed to update entry: %v", err)
		}
	}
}

func BenchmarkMemoryStoreQuerySimple(b *testing.B) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}
	
	// 预填充数据
	for i := 0; i < 1000; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: "Player",
			Level:      10,
			Prosperity: int32(1000 + i),
		}
		
		store.UpdateEntry(2, entry, config)
	}
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		_, _, err := store.GetRankList(2, 1, 50)
		if err != nil {
			b.Fatalf("Failed to get rank list: %v", err)
		}
	}
}
