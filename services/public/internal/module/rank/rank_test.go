package rank

import (
	"context"
	"testing"
	"time"
)

func TestMemoryStore(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	// 测试更新条目
	entry1 := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 1500,
	}

	newRank, oldRank, err := store.UpdateEntry(2, entry1, config)
	if err != nil {
		t.Fatalf("Failed to update entry: %v", err)
	}

	if newRank != 1 {
		t.<PERSON><PERSON><PERSON>("Expected rank 1, got %d", newRank)
	}

	if oldRank != -1 {
		t.<PERSON><PERSON><PERSON>("Expected old rank -1, got %d", oldRank)
	}

	// 测试获取排行榜列表
	entries, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if len(entries) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 entry, got %d", len(entries))
	}

	if totalCount != 1 {
		t.<PERSON><PERSON><PERSON>("Expected total count 1, got %d", totalCount)
	}

	// 测试获取玩家排名
	rank, entry, err := store.GetPlayerRank(2, 1001)
	if err != nil {
		t.Fatalf("Failed to get player rank: %v", err)
	}

	if rank != 1 {
		t.Errorf("Expected rank 1, got %d", rank)
	}

	if entry.PlayerID != 1001 {
		t.Errorf("Expected player ID 1001, got %d", entry.PlayerID)
	}
}

func TestUpdateQueue(t *testing.T) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
		QueueSize:     100,
		WorkerCount:   2,
		BatchSize:     5,
	}

	queue := NewUpdateQueue(2, config, store)
	queue.Start()
	defer queue.Stop()

	// 添加更新请求
	entry := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 1500,
	}

	request := &UpdateRequest{
		RankType: 2,
		Entry:    entry,
		Priority: 0,
	}

	success := queue.AddUpdateRequest(request)
	if !success {
		t.Fatal("Failed to add update request")
	}

	// 等待处理完成
	time.Sleep(1 * time.Second)

	// 检查统计
	stats := queue.GetStats()
	if stats.ProcessedCount == 0 {
		t.Error("Expected processed count > 0")
	}
}

func TestQueueManager(t *testing.T) {
	manager := NewQueueManager()

	// 添加配置
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxQueryLimit: 500,
		ShowRankLimit: 300,
		MaxRankLimit:  1000,
		QueueSize:     100,
		WorkerCount:   2,
		BatchSize:     5,
	}

	err := manager.AddRankConfig(config)
	if err != nil {
		t.Fatalf("Failed to add rank config: %v", err)
	}

	// 启动管理器
	err = manager.Start()
	if err != nil {
		t.Fatalf("Failed to start queue manager: %v", err)
	}
	defer manager.Stop()

	ctx := context.Background()

	// 更新排行榜
	entry := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 1500,
	}

	err = manager.UpdateRank(ctx, 2, entry)
	if err != nil {
		t.Fatalf("Failed to update rank: %v", err)
	}

	// 等待处理完成
	time.Sleep(1 * time.Second)

	// 获取排行榜列表
	entries, totalCount, err := manager.GetRankList(ctx, 2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if len(entries) != 1 {
		t.Errorf("Expected 1 entry, got %d", len(entries))
	}

	if totalCount != 1 {
		t.Errorf("Expected total count 1, got %d", totalCount)
	}

	// 获取玩家排名
	rank, playerEntry, err := manager.GetPlayerRank(ctx, 2, 1001)
	if err != nil {
		t.Fatalf("Failed to get player rank: %v", err)
	}

	if rank != 1 {
		t.Errorf("Expected rank 1, got %d", rank)
	}

	if playerEntry.PlayerID != 1001 {
		t.Errorf("Expected player ID 1001, got %d", playerEntry.PlayerID)
	}
}

func TestRankManager(t *testing.T) {
	// 跳过这个测试，因为需要game_config模块和配置文件
	t.Skip("Skipping TestRankManager - requires game_config and config files")
}

func BenchmarkMemoryStoreUpdate(b *testing.B) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: "Player",
			Level:      10,
			Prosperity: int32(1000 + i),
		}

		_, _, err := store.UpdateEntry(2, entry, config)
		if err != nil {
			b.Fatalf("Failed to update entry: %v", err)
		}
	}
}

func BenchmarkMemoryStoreQuery(b *testing.B) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	// 预填充数据
	for i := 0; i < 1000; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: "Player",
			Level:      10,
			Prosperity: int32(1000 + i),
		}

		store.UpdateEntry(2, entry, config)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _, err := store.GetRankList(2, 1, 50)
		if err != nil {
			b.Fatalf("Failed to get rank list: %v", err)
		}
	}
}

func BenchmarkQueueThroughput(b *testing.B) {
	store := NewMemoryStore()
	config := &Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
		QueueSize:     10000,
		WorkerCount:   4,
		BatchSize:     20,
	}

	queue := NewUpdateQueue(2, config, store)
	queue.Start()
	defer queue.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: "Player",
			Level:      10,
			Prosperity: int32(1000 + i),
		}

		request := &UpdateRequest{
			RankType: 2,
			Entry:    entry,
			Priority: 0,
		}

		queue.AddUpdateRequest(request)
	}

	// 等待处理完成
	time.Sleep(1 * time.Second)
}
