package rank

import (
	"fmt"
	"sort"
	"sync"
	"testing"
	"time"
)

// TestPlayerInfo represents basic player information (for testing)
type TestPlayerInfo struct {
	PlayerID   uint64 // 玩家ID
	PlayerName string // 玩家名称
	Level      int32  // 玩家等级
	Avatar     string // 玩家头像
	UpdateTime int64  // 信息更新时间
}

// TestRankEntry represents an entry in a specific leaderboard (for testing)
type TestRankEntry struct {
	PlayerID   uint64 // 玩家ID
	Score      int32  // 分数（如繁荣度、充值金额等）
	UpdateTime int64  // 分数更新时间
	Ranking    int32  // 排名
}

// TestEntry represents a complete leaderboard entry (for testing)
type TestEntry struct {
	PlayerInfo *TestPlayerInfo // 玩家基础信息
	RankEntry  *TestRankEntry  // 排行榜条目信息
}

// NewTestEntry creates a new test entry
func NewTestEntry(playerID uint64, playerName string, level int32, score int32) *TestEntry {
	return &TestEntry{
		PlayerInfo: &TestPlayerInfo{
			PlayerID:   playerID,
			PlayerName: playerName,
			Level:      level,
			Avatar:     "",
			UpdateTime: time.Now().Unix(),
		},
		RankEntry: &TestRankEntry{
			PlayerID:   playerID,
			Score:      score,
			UpdateTime: time.Now().Unix(),
		},
	}
}

// SimpleMemoryStore 简化的内存存储，不依赖外部包
type SimpleMemoryStore struct {
	rankData  map[int32]*SimpleMemoryRankData
	rankMutex map[int32]*sync.RWMutex
	globalMutex sync.RWMutex
}

// SimpleMemoryRankData 简化的排行榜数据
type SimpleMemoryRankData struct {
	Entries    []*TestRankEntry
	PlayerMap  map[uint64]*TestRankEntry
	TotalCount int64
	UpdateTime int64
}

// SimpleConfig 简化的配置
type SimpleConfig struct {
	RankType      int32
	MinScoreLimit int32
	MaxRankLimit  int32
}

// NewSimpleMemoryStore 创建简化的内存存储
func NewSimpleMemoryStore() *SimpleMemoryStore {
	return &SimpleMemoryStore{
		rankData:  make(map[int32]*SimpleMemoryRankData),
		rankMutex: make(map[int32]*sync.RWMutex),
	}
}

// NewSimpleMemoryRankData 创建简化的排行榜数据
func NewSimpleMemoryRankData() *SimpleMemoryRankData {
	return &SimpleMemoryRankData{
		Entries:    make([]*TestRankEntry, 0),
		PlayerMap:  make(map[uint64]*TestRankEntry),
		TotalCount: 0,
		UpdateTime: time.Now().Unix(),
	}
}

// getRankMutex 获取指定排行榜的锁
func (ms *SimpleMemoryStore) getRankMutex(rankType int32) *sync.RWMutex {
	ms.globalMutex.RLock()
	mutex, exists := ms.rankMutex[rankType]
	ms.globalMutex.RUnlock()

	if !exists {
		ms.globalMutex.Lock()
		if mutex, exists = ms.rankMutex[rankType]; !exists {
			mutex = &sync.RWMutex{}
			ms.rankMutex[rankType] = mutex
		}
		ms.globalMutex.Unlock()
	}

	return mutex
}

// getRankData 获取指定排行榜的数据
func (ms *SimpleMemoryStore) getRankData(rankType int32) *SimpleMemoryRankData {
	ms.globalMutex.RLock()
	data, exists := ms.rankData[rankType]
	ms.globalMutex.RUnlock()

	if !exists {
		ms.globalMutex.Lock()
		if data, exists = ms.rankData[rankType]; !exists {
			data = NewSimpleMemoryRankData()
			ms.rankData[rankType] = data
		}
		ms.globalMutex.Unlock()
	}

	return data
}

// UpdateEntry 更新排行榜条目
func (ms *SimpleMemoryStore) UpdateEntry(rankType int32, entry *TestEntry, config *SimpleConfig) (int32, int32, error) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.Lock()
	defer mutex.Unlock()

	// 检查分数是否达到上榜要求
	if entry.Prosperity < config.MinScoreLimit {
		if _, exists := data.PlayerMap[entry.PlayerID]; exists {
			oldRank := ms.findPlayerRank(data, entry.PlayerID)
			ms.removeEntry(data, entry.PlayerID)
			return -1, oldRank, nil
		}
		return -1, -1, nil
	}

	// 记录旧排名
	oldRank := int32(-1)
	if _, exists := data.PlayerMap[entry.PlayerID]; exists {
		oldRank = ms.findPlayerRank(data, entry.PlayerID)
	}

	// 更新或添加条目
	entry.UpdateTime = time.Now().Unix()
	data.PlayerMap[entry.PlayerID] = entry

	// 重新构建排序列表
	ms.rebuildSortedList(data, config)

	// 查找新排名
	newRank := ms.findPlayerRank(data, entry.PlayerID)

	return newRank, oldRank, nil
}

// removeEntry 移除条目
func (ms *SimpleMemoryStore) removeEntry(data *SimpleMemoryRankData, playerID uint64) {
	delete(data.PlayerMap, playerID)

	// 从排序列表中移除
	for i, entry := range data.Entries {
		if entry.PlayerID == playerID {
			data.Entries = append(data.Entries[:i], data.Entries[i+1:]...)
			break
		}
	}

	data.TotalCount = int64(len(data.Entries))
	data.UpdateTime = time.Now().Unix()
}

// rebuildSortedList 重新构建排序列表
func (ms *SimpleMemoryStore) rebuildSortedList(data *SimpleMemoryRankData, config *SimpleConfig) {
	// 将所有条目放入切片
	entries := make([]*Entry, 0, len(data.PlayerMap))
	for _, entry := range data.PlayerMap {
		entries = append(entries, entry)
	}

	// 按分数降序排序
	sort.Slice(entries, func(i, j int) bool {
		if entries[i].Prosperity == entries[j].Prosperity {
			return entries[i].UpdateTime < entries[j].UpdateTime
		}
		return entries[i].Prosperity > entries[j].Prosperity
	})

	// 限制排行榜大小
	if int32(len(entries)) > config.MaxRankLimit {
		for i := config.MaxRankLimit; i < int32(len(entries)); i++ {
			delete(data.PlayerMap, entries[i].PlayerID)
		}
		entries = entries[:config.MaxRankLimit]
	}

	// 更新排名
	for i, entry := range entries {
		entry.Ranking = int32(i + 1)
	}

	data.Entries = entries
	data.TotalCount = int64(len(entries))
	data.UpdateTime = time.Now().Unix()
}

// findPlayerRank 查找玩家排名
func (ms *SimpleMemoryStore) findPlayerRank(data *SimpleMemoryRankData, playerID uint64) int32 {
	for i, entry := range data.Entries {
		if entry.PlayerID == playerID {
			return int32(i + 1)
		}
	}
	return -1
}

// GetRankList 获取排行榜列表
func (ms *SimpleMemoryStore) GetRankList(rankType int32, start, count int32) ([]*Entry, int64, error) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.RLock()
	defer mutex.RUnlock()

	if start < 1 {
		start = 1
	}

	startIndex := start - 1
	endIndex := startIndex + count

	if startIndex >= int32(len(data.Entries)) {
		return []*Entry{}, data.TotalCount, nil
	}

	if endIndex > int32(len(data.Entries)) {
		endIndex = int32(len(data.Entries))
	}

	// 复制条目以避免外部修改
	result := make([]*Entry, endIndex-startIndex)
	for i := startIndex; i < endIndex; i++ {
		entry := *data.Entries[i]
		result[i-startIndex] = &entry
	}

	return result, data.TotalCount, nil
}

// GetPlayerRank 获取玩家排名
func (ms *SimpleMemoryStore) GetPlayerRank(rankType int32, playerID uint64) (int32, *Entry, error) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.RLock()
	defer mutex.RUnlock()

	entry, exists := data.PlayerMap[playerID]
	if !exists {
		return -1, nil, nil
	}

	rank := ms.findPlayerRank(data, playerID)

	// 复制条目以避免外部修改
	entryCopy := *entry
	return rank, &entryCopy, nil
}

// 测试基本功能
func TestSimpleMemoryStore(t *testing.T) {
	store := NewSimpleMemoryStore()
	config := &SimpleConfig{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	// 测试更新条目
	entry1 := &Entry{
		PlayerID:   1001,
		PlayerName: "Player1",
		Level:      10,
		Prosperity: 1500,
	}

	newRank, oldRank, err := store.UpdateEntry(2, entry1, config)
	if err != nil {
		t.Fatalf("Failed to update entry: %v", err)
	}

	if newRank != 1 {
		t.Errorf("Expected rank 1, got %d", newRank)
	}

	if oldRank != -1 {
		t.Errorf("Expected old rank -1, got %d", oldRank)
	}

	// 测试获取排行榜列表
	entries, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if len(entries) != 1 {
		t.Errorf("Expected 1 entry, got %d", len(entries))
	}

	if totalCount != 1 {
		t.Errorf("Expected total count 1, got %d", totalCount)
	}

	// 测试获取玩家排名
	rank, entry, err := store.GetPlayerRank(2, 1001)
	if err != nil {
		t.Fatalf("Failed to get player rank: %v", err)
	}

	if rank != 1 {
		t.Errorf("Expected rank 1, got %d", rank)
	}

	if entry.PlayerID != 1001 {
		t.Errorf("Expected player ID 1001, got %d", entry.PlayerID)
	}
}

// 测试多个条目的排序
func TestSimpleMemoryStoreOrdering(t *testing.T) {
	store := NewSimpleMemoryStore()
	config := &SimpleConfig{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	// 添加多个条目
	entries := []*Entry{
		{PlayerID: 1001, PlayerName: "Player1", Level: 10, Prosperity: 1500},
		{PlayerID: 1002, PlayerName: "Player2", Level: 15, Prosperity: 2000},
		{PlayerID: 1003, PlayerName: "Player3", Level: 12, Prosperity: 1800},
		{PlayerID: 1004, PlayerName: "Player4", Level: 8, Prosperity: 1200},
		{PlayerID: 1005, PlayerName: "Player5", Level: 20, Prosperity: 2500},
	}

	for _, entry := range entries {
		_, _, err := store.UpdateEntry(2, entry, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", entry.PlayerID, err)
		}
	}

	// 检查排序是否正确（按繁荣度降序）
	rankList, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if totalCount != 5 {
		t.Errorf("Expected total count 5, got %d", totalCount)
	}

	if len(rankList) != 5 {
		t.Errorf("Expected 5 entries, got %d", len(rankList))
	}

	// 验证排序：Player5(2500) > Player2(2000) > Player3(1800) > Player1(1500) > Player4(1200)
	expectedOrder := []uint64{1005, 1002, 1003, 1001, 1004}
	expectedScores := []int32{2500, 2000, 1800, 1500, 1200}

	for i, entry := range rankList {
		if entry.PlayerID != expectedOrder[i] {
			t.Errorf("Expected player %d at rank %d, got player %d", expectedOrder[i], i+1, entry.PlayerID)
		}
		if entry.Prosperity != expectedScores[i] {
			t.Errorf("Expected score %d for player %d, got %d", expectedScores[i], entry.PlayerID, entry.Prosperity)
		}
		if entry.Ranking != int32(i+1) {
			t.Errorf("Expected ranking %d for player %d, got %d", i+1, entry.PlayerID, entry.Ranking)
		}
	}
}

// 测试并发安全性
func TestSimpleMemoryStoreConcurrency(t *testing.T) {
	store := NewSimpleMemoryStore()
	config := &SimpleConfig{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	const numGoroutines = 10
	const numUpdatesPerGoroutine = 100

	var wg sync.WaitGroup

	// 并发更新
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < numUpdatesPerGoroutine; j++ {
				playerID := uint64(goroutineID*1000 + j)
				entry := &Entry{
					PlayerID:   playerID,
					PlayerName: fmt.Sprintf("Player%d", playerID),
					Level:      int32(10 + j%20),
					Prosperity: int32(1000 + j*10),
				}

				_, _, err := store.UpdateEntry(2, entry, config)
				if err != nil {
					t.Errorf("Failed to update entry for player %d: %v", playerID, err)
				}
			}
		}(i)
	}

	wg.Wait()

	// 验证最终状态
	entries, totalCount, err := store.GetRankList(2, 1, 100)
	if err != nil {
		t.Fatalf("Failed to get final rank list: %v", err)
	}

	if totalCount == 0 {
		t.Error("Expected some entries after concurrent updates")
	}

	// 验证排序正确性
	for i := 1; i < len(entries); i++ {
		if entries[i-1].Prosperity < entries[i].Prosperity {
			t.Errorf("Entries not properly sorted: entry %d has score %d, entry %d has score %d",
				i-1, entries[i-1].Prosperity, i, entries[i].Prosperity)
		}
	}
}

// 性能基准测试
func BenchmarkSimpleMemoryStoreUpdate(b *testing.B) {
	store := NewSimpleMemoryStore()
	config := &SimpleConfig{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: fmt.Sprintf("Player%d", i),
			Level:      int32(10 + i%50),
			Prosperity: int32(1000 + i*10),
		}

		_, _, err := store.UpdateEntry(2, entry, config)
		if err != nil {
			b.Fatalf("Failed to update entry: %v", err)
		}
	}
}

func BenchmarkSimpleMemoryStoreQuery(b *testing.B) {
	store := NewSimpleMemoryStore()
	config := &SimpleConfig{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  10000,
	}

	// 预填充数据
	for i := 0; i < 1000; i++ {
		entry := &Entry{
			PlayerID:   uint64(1000 + i),
			PlayerName: fmt.Sprintf("Player%d", i),
			Level:      int32(10 + i%50),
			Prosperity: int32(1000 + i*10),
		}

		store.UpdateEntry(2, entry, config)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _, err := store.GetRankList(2, 1, 50)
		if err != nil {
			b.Fatalf("Failed to get rank list: %v", err)
		}
	}
}
