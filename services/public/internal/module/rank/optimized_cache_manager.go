package rank

import (
	"context"
	"hash/fnv"
	"kairo_paradise_server/internal/logger"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// ShardedCacheManager 分片缓存管理器（减少锁竞争）
type ShardedCacheManager struct {
	// 热点排行榜缓存 (前N名) - 按排行榜类型分片
	hotRankCaches map[int32]*Cache
	hotRankMutex  sync.RWMutex

	// 玩家排名缓存 - 分片存储
	playerCacheShards []*PlayerCacheShard
	shardCount        int32

	// 缓存配置
	hotCacheSize    int32 // 热点缓存大小
	cacheExpireTime int64 // 缓存过期时间

	// 统计信息（使用原子操作）
	hitCount  int64
	missCount int64
	setCount  int64
}

// PlayerCacheShard 玩家缓存分片
type PlayerCacheShard struct {
	cache map[uint64]*PlayerRankCache
	mutex sync.RWMutex
}

// NewShardedCacheManager 创建分片缓存管理器
func NewShardedCacheManager() *ShardedCacheManager {
	return NewShardedCacheManagerWithConfig(100, 300, 16) // 默认16个分片
}

// NewShardedCacheManagerWithConfig 使用配置创建分片缓存管理器
func NewShardedCacheManagerWithConfig(hotCacheSize int32, cacheExpireTime int64, shardCount int32) *ShardedCacheManager {
	if shardCount <= 0 {
		shardCount = 16 // 默认16个分片
	}

	shards := make([]*PlayerCacheShard, shardCount)
	for i := int32(0); i < shardCount; i++ {
		shards[i] = &PlayerCacheShard{
			cache: make(map[uint64]*PlayerRankCache),
		}
	}

	return &ShardedCacheManager{
		hotRankCaches:     make(map[int32]*Cache),
		playerCacheShards: shards,
		shardCount:        shardCount,
		hotCacheSize:      hotCacheSize,
		cacheExpireTime:   cacheExpireTime,
	}
}

// getPlayerShard 根据玩家ID获取对应的分片
func (scm *ShardedCacheManager) getPlayerShard(playerID uint64) *PlayerCacheShard {
	hash := fnv.New32a()
	hash.Write([]byte{byte(playerID), byte(playerID >> 8), byte(playerID >> 16), byte(playerID >> 24),
		byte(playerID >> 32), byte(playerID >> 40), byte(playerID >> 48), byte(playerID >> 56)})
	shardIndex := hash.Sum32() % uint32(scm.shardCount)
	return scm.playerCacheShards[shardIndex]
}

// GetHotRankCache 获取热点排行榜缓存
func (scm *ShardedCacheManager) GetHotRankCache(rankType int32) *Cache {
	scm.hotRankMutex.RLock()
	defer scm.hotRankMutex.RUnlock()

	cache, exists := scm.hotRankCaches[rankType]
	if !exists || scm.isCacheExpired(cache.UpdateTime) {
		atomic.AddInt64(&scm.missCount, 1)
		return nil
	}
	
	atomic.AddInt64(&scm.hitCount, 1)
	return cache
}

// SetHotRankCache 设置热点排行榜缓存
func (scm *ShardedCacheManager) SetHotRankCache(rankType int32, entries []*Entry, totalCount int64) {
	scm.hotRankMutex.Lock()
	defer scm.hotRankMutex.Unlock()

	// 只缓存前N名
	cacheEntries := entries
	if int32(len(entries)) > scm.hotCacheSize {
		cacheEntries = entries[:scm.hotCacheSize]
	}

	cache := &Cache{
		Entries:    cacheEntries,
		TotalCount: totalCount,
		UpdateTime: time.Now().Unix(),
	}

	scm.hotRankCaches[rankType] = cache
	atomic.AddInt64(&scm.setCount, 1)
	
	logger.Debug("Set hot rank cache",
		zap.Int32("rankType", rankType),
		zap.Int("entryCount", len(cacheEntries)))
}

// GetPlayerRankCache 获取玩家排名缓存
func (scm *ShardedCacheManager) GetPlayerRankCache(playerID uint64) *PlayerRankCache {
	shard := scm.getPlayerShard(playerID)
	shard.mutex.RLock()
	defer shard.mutex.RUnlock()

	cache, exists := shard.cache[playerID]
	if !exists || scm.isCacheExpired(cache.UpdateTime) {
		atomic.AddInt64(&scm.missCount, 1)
		return nil
	}
	
	atomic.AddInt64(&scm.hitCount, 1)
	return cache
}

// SetPlayerRankCache 设置玩家排名缓存
func (scm *ShardedCacheManager) SetPlayerRankCache(playerID uint64, rank int32, score int32) {
	shard := scm.getPlayerShard(playerID)
	shard.mutex.Lock()
	defer shard.mutex.Unlock()

	cache := &PlayerRankCache{
		Rank:       rank,
		Score:      score,
		UpdateTime: time.Now().Unix(),
	}

	shard.cache[playerID] = cache
	atomic.AddInt64(&scm.setCount, 1)
	
	logger.Debug("Set player rank cache",
		zap.Uint64("playerID", playerID),
		zap.Int32("rank", rank),
		zap.Int32("score", score))
}

// ClearRankCache 清除排行榜相关缓存
func (scm *ShardedCacheManager) ClearRankCache(rankType int32) {
	scm.hotRankMutex.Lock()
	defer scm.hotRankMutex.Unlock()

	// 清除热点排行榜缓存
	delete(scm.hotRankCaches, rankType)
	logger.Debug("Cleared rank cache", zap.Int32("rankType", rankType))
}

// ClearExpiredCache 清除过期缓存（并行处理各分片）
func (scm *ShardedCacheManager) ClearExpiredCache() {
	now := time.Now().Unix()
	
	// 清除过期的热点排行榜缓存
	scm.hotRankMutex.Lock()
	expiredHotCount := 0
	for rankType, cache := range scm.hotRankCaches {
		if now-cache.UpdateTime > scm.cacheExpireTime {
			delete(scm.hotRankCaches, rankType)
			expiredHotCount++
		}
	}
	scm.hotRankMutex.Unlock()

	// 并行清除各分片的过期玩家缓存
	var wg sync.WaitGroup
	expiredPlayerCount := int64(0)
	
	for _, shard := range scm.playerCacheShards {
		wg.Add(1)
		go func(s *PlayerCacheShard) {
			defer wg.Done()
			
			s.mutex.Lock()
			defer s.mutex.Unlock()
			
			localExpiredCount := 0
			for playerID, cache := range s.cache {
				if now-cache.UpdateTime > scm.cacheExpireTime {
					delete(s.cache, playerID)
					localExpiredCount++
				}
			}
			
			atomic.AddInt64(&expiredPlayerCount, int64(localExpiredCount))
		}(shard)
	}
	
	wg.Wait()
	
	totalExpired := expiredHotCount + int(expiredPlayerCount)
	if totalExpired > 0 {
		logger.Debug("Cleared expired cache",
			zap.Int("hotCacheExpired", expiredHotCount),
			zap.Int64("playerCacheExpired", expiredPlayerCount),
			zap.Int("totalExpired", totalExpired))
	}
}

// GetCacheStats 获取缓存统计信息
func (scm *ShardedCacheManager) GetCacheStats() map[string]interface{} {
	scm.hotRankMutex.RLock()
	hotCacheCount := len(scm.hotRankCaches)
	scm.hotRankMutex.RUnlock()

	// 统计所有分片的玩家缓存数量
	totalPlayerCacheCount := 0
	for _, shard := range scm.playerCacheShards {
		shard.mutex.RLock()
		totalPlayerCacheCount += len(shard.cache)
		shard.mutex.RUnlock()
	}

	hits := atomic.LoadInt64(&scm.hitCount)
	misses := atomic.LoadInt64(&scm.missCount)
	sets := atomic.LoadInt64(&scm.setCount)
	
	var hitRate float64
	if hits+misses > 0 {
		hitRate = float64(hits) / float64(hits+misses) * 100
	}

	return map[string]interface{}{
		"hot_rank_cache_count":    hotCacheCount,
		"player_rank_cache_count": totalPlayerCacheCount,
		"hot_cache_size":          scm.hotCacheSize,
		"cache_expire_time":       scm.cacheExpireTime,
		"shard_count":             scm.shardCount,
		"hit_count":               hits,
		"miss_count":              misses,
		"set_count":               sets,
		"hit_rate":                hitRate,
	}
}

// BatchGetPlayerRanks 批量获取玩家排名（优化版，减少锁竞争）
func (scm *ShardedCacheManager) BatchGetPlayerRanks(ctx context.Context, playerIDs []uint64,
	fallbackFunc func(context.Context, []uint64) ([]*RankInfo, error)) ([]*RankInfo, error) {

	results := make([]*RankInfo, len(playerIDs))
	uncachedIDs := make([]uint64, 0)
	uncachedIndices := make([]int, 0)

	// 按分片分组玩家ID，减少锁竞争
	shardGroups := make(map[int32][]struct {
		playerID uint64
		index    int
	})

	for i, playerID := range playerIDs {
		shardIndex := int32(fnv.New32a().Sum32() % uint32(scm.shardCount))
		if shardGroups[shardIndex] == nil {
			shardGroups[shardIndex] = make([]struct {
				playerID uint64
				index    int
			}, 0)
		}
		shardGroups[shardIndex] = append(shardGroups[shardIndex], struct {
			playerID uint64
			index    int
		}{playerID, i})
	}

	// 并行处理各分片
	var wg sync.WaitGroup
	var uncachedMutex sync.Mutex

	for shardIndex, group := range shardGroups {
		wg.Add(1)
		go func(sIdx int32, g []struct {
			playerID uint64
			index    int
		}) {
			defer wg.Done()

			shard := scm.playerCacheShards[sIdx]
			shard.mutex.RLock()
			defer shard.mutex.RUnlock()

			for _, item := range g {
				cache, exists := shard.cache[item.playerID]
				if exists && !scm.isCacheExpired(cache.UpdateTime) {
					atomic.AddInt64(&scm.hitCount, 1)
					if cache.Rank == -1 {
						results[item.index] = &RankInfo{Rank: -1}
					} else {
						results[item.index] = &RankInfo{
							Rank: cache.Rank,
							Entry: Entry{
								PlayerInfo: &PlayerInfo{PlayerID: item.playerID},
								RankEntry:  &RankEntry{Score: cache.Score},
							},
						}
					}
				} else {
					atomic.AddInt64(&scm.missCount, 1)
					uncachedMutex.Lock()
					uncachedIDs = append(uncachedIDs, item.playerID)
					uncachedIndices = append(uncachedIndices, item.index)
					uncachedMutex.Unlock()
				}
			}
		}(shardIndex, group)
	}

	wg.Wait()

	// 如果有未缓存的数据，调用fallback函数
	if len(uncachedIDs) > 0 {
		uncachedResults, err := fallbackFunc(ctx, uncachedIDs)
		if err != nil {
			return nil, err
		}

		// 将结果填入对应位置并缓存
		for i, result := range uncachedResults {
			idx := uncachedIndices[i]
			playerID := uncachedIDs[i]
			results[idx] = result

			// 缓存排名结果
			if result.Rank == -1 {
				scm.SetPlayerRankCache(playerID, -1, 0)
			} else {
				scm.SetPlayerRankCache(playerID, result.Rank, result.Entry.RankEntry.Score)
			}
		}
	}

	return results, nil
}

// 辅助方法
func (scm *ShardedCacheManager) isCacheExpired(updateTime int64) bool {
	return time.Now().Unix()-updateTime > scm.cacheExpireTime
}

// StartCacheCleanup 启动缓存清理任务
func (scm *ShardedCacheManager) StartCacheCleanup(stopCh <-chan struct{}) {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			scm.ClearExpiredCache()
		case <-stopCh:
			return
		}
	}
}
