package rank

import (
	"context"
	"fmt"
	"time"
)

// ExampleUsageWithoutDependencies 展示如何使用排行榜系统（不依赖外部配置）
func ExampleUsageWithoutDependencies() {
	fmt.Println("=== 排行榜队列系统使用示例 ===")

	// 1. 创建队列管理器
	fmt.Println("\n1. 创建队列管理器")
	manager := NewQueueManager()

	// 2. 添加排行榜配置
	fmt.Println("2. 添加排行榜配置")

	// 繁荣榜配置
	prosperityConfig := &Config{
		RankType:       2,    // 繁荣榜
		MinScoreLimit:  100,  // 最低100分上榜
		MaxQueryLimit:  500,  // 最多查询500条
		ShowRankLimit:  300,  // 显示前300名
		MaxRankLimit:   1000, // 最多1000名
		UpdateInterval: 0,    // 实时更新
		IsRealtime:     true, // 实时排行榜
		QueueSize:      2000, // 队列大小2000
		WorkerCount:    3,    // 3个工作器
		BatchSize:      20,   // 批处理大小20
	}

	err := manager.AddRankConfig(prosperityConfig)
	if err != nil {
		fmt.Printf("添加繁荣榜配置失败: %v\n", err)
		return
	}

	// 充值榜配置
	rechargeConfig := &Config{
		RankType:       3,     // 充值榜
		MinScoreLimit:  10,    // 最低10分上榜
		MaxQueryLimit:  500,   // 最多查询500条
		ShowRankLimit:  300,   // 显示前300名
		MaxRankLimit:   1000,  // 最多1000名
		UpdateInterval: 5,     // 5秒更新间隔
		IsRealtime:     false, // 定时排行榜
		QueueSize:      1500,  // 队列大小1500
		WorkerCount:    2,     // 2个工作器
		BatchSize:      15,    // 批处理大小15
	}

	err = manager.AddRankConfig(rechargeConfig)
	if err != nil {
		fmt.Printf("添加充值榜配置失败: %v\n", err)
		return
	}

	// 3. 启动队列管理器
	fmt.Println("3. 启动队列管理器")
	err = manager.Start()
	if err != nil {
		fmt.Printf("启动队列管理器失败: %v\n", err)
		return
	}
	defer manager.Stop()

	// 等待启动完成
	time.Sleep(100 * time.Millisecond)

	ctx := context.Background()

	// 4. 添加测试数据
	fmt.Println("4. 添加测试数据")

	testPlayers := []*Entry{
		NewEntry(1001, "张三", 10, "avatar1.jpg", 1500),
		NewEntry(1002, "李四", 15, "avatar2.jpg", 2000),
		NewEntry(1003, "王五", 12, "avatar3.jpg", 1800),
		NewEntry(1004, "赵六", 8, "avatar4.jpg", 1200),
		NewEntry(1005, "钱七", 20, "avatar5.jpg", 2500),
		NewEntry(1006, "孙八", 5, "avatar6.jpg", 800),
		NewEntry(1007, "周九", 25, "avatar7.jpg", 3000),
		NewEntry(1008, "吴十", 18, "avatar8.jpg", 2200),
	}

	// 更新繁荣榜
	fmt.Println("更新繁荣榜数据...")
	for _, player := range testPlayers {
		err := manager.UpdateRank(ctx, 2, player)
		if err != nil {
			fmt.Printf("更新玩家 %s 的繁荣榜失败: %v\n", player.PlayerInfo.PlayerName, err)
		} else {
			fmt.Printf("更新玩家 %s (ID:%d) 繁荣度:%d\n", player.PlayerInfo.PlayerName, player.PlayerInfo.PlayerID, player.RankEntry.Score)
		}
	}

	// 等待队列处理完成
	fmt.Println("等待队列处理完成...")
	time.Sleep(2 * time.Second)

	// 5. 查询排行榜
	fmt.Println("\n5. 查询排行榜")

	// 获取繁荣榜前5名
	fmt.Println("=== 繁荣榜前5名 ===")
	entries, totalCount, err := manager.GetRankList(ctx, 2, 1, 5)
	if err != nil {
		fmt.Printf("获取繁荣榜失败: %v\n", err)
	} else {
		fmt.Printf("总共 %d 名玩家上榜\n", totalCount)
		for i, entry := range entries {
			fmt.Printf("第%d名: %s (ID:%d) 繁荣度:%d\n",
				i+1, entry.PlayerInfo.PlayerName, entry.PlayerInfo.PlayerID, entry.RankEntry.Score)
		}
	}

	// 6. 查询特定玩家排名
	fmt.Println("\n6. 查询特定玩家排名")

	testPlayerIDs := []uint64{1002, 1005, 1006}
	for _, playerID := range testPlayerIDs {
		rank, entry, err := manager.GetPlayerRank(ctx, 2, playerID)
		if err != nil {
			fmt.Printf("查询玩家 %d 排名失败: %v\n", playerID, err)
		} else if entry == nil {
			fmt.Printf("玩家 %d 未上榜\n", playerID)
		} else {
			fmt.Printf("玩家 %s (ID:%d) 排名: 第%d名, 繁荣度:%d\n",
				entry.PlayerInfo.PlayerName, entry.PlayerInfo.PlayerID, rank, entry.RankEntry.Score)
		}
	}

	// 7. 获取队列统计信息
	fmt.Println("\n7. 队列统计信息")

	allStats := manager.GetAllQueueStats()
	for rankType, stats := range allStats {
		rankName := "未知"
		switch rankType {
		case 2:
			rankName = "繁荣榜"
		case 3:
			rankName = "充值榜"
		}

		fmt.Printf("%s (类型:%d):\n", rankName, rankType)
		fmt.Printf("  当前队列长度: %d\n", stats.QueueSize)
		fmt.Printf("  已处理请求数: %d\n", stats.ProcessedCount)
		fmt.Printf("  错误请求数: %d\n", stats.ErrorCount)
		fmt.Printf("  工作器数量: %d\n", stats.WorkerCount)

		if stats.ProcessedCount > 0 {
			errorRate := float64(stats.ErrorCount) / float64(stats.ProcessedCount) * 100
			fmt.Printf("  错误率: %.2f%%\n", errorRate)
		}
		fmt.Println()
	}

	// 8. 压力测试
	fmt.Println("8. 压力测试")

	fmt.Println("开始压力测试，添加1000个玩家...")
	startTime := time.Now()

	for i := 0; i < 1000; i++ {
		player := NewEntry(
			uint64(2000 + i),
			fmt.Sprintf("测试玩家%d", i),
			int32(1 + i%50),
			fmt.Sprintf("avatar%d.jpg", i),
			int32(500 + i*5),
		)

		err := manager.UpdateRank(ctx, 2, player)
		if err != nil {
			fmt.Printf("压力测试更新失败: %v\n", err)
		}
	}

	duration := time.Since(startTime)
	fmt.Printf("压力测试完成，耗时: %v\n", duration)
	fmt.Printf("平均每秒处理: %.2f 个更新请求\n", 1000.0/duration.Seconds())

	// 等待队列处理完成
	fmt.Println("等待压力测试队列处理完成...")
	time.Sleep(5 * time.Second)

	// 9. 最终统计
	fmt.Println("\n9. 最终统计")

	finalEntries, finalTotalCount, err := manager.GetRankList(ctx, 2, 1, 10)
	if err != nil {
		fmt.Printf("获取最终排行榜失败: %v\n", err)
	} else {
		fmt.Printf("最终排行榜总人数: %d\n", finalTotalCount)
		fmt.Println("=== 最终繁荣榜前10名 ===")
		for i, entry := range finalEntries {
			fmt.Printf("第%d名: %s (ID:%d) 繁荣度:%d\n",
				i+1, entry.PlayerInfo.PlayerName, entry.PlayerInfo.PlayerID, entry.RankEntry.Score)
		}
	}

	// 最终队列统计
	finalStats := manager.GetAllQueueStats()
	for rankType, stats := range finalStats {
		if rankType == 2 { // 只显示繁荣榜
			fmt.Printf("\n繁荣榜最终统计:\n")
			fmt.Printf("  总处理请求数: %d\n", stats.ProcessedCount)
			fmt.Printf("  总错误数: %d\n", stats.ErrorCount)
			fmt.Printf("  当前队列长度: %d\n", stats.QueueSize)

			if stats.ProcessedCount > 0 {
				successRate := float64(stats.ProcessedCount-stats.ErrorCount) / float64(stats.ProcessedCount) * 100
				fmt.Printf("  成功率: %.2f%%\n", successRate)
			}
		}
	}

	fmt.Println("\n=== 排行榜队列系统示例完成 ===")
}

// RunExample 运行示例（可以在main函数中调用）
func RunExample() {
	ExampleUsageWithoutDependencies()
}

// PerformanceTest 性能测试
func PerformanceTest(playerCount int, updateCount int) {
	fmt.Printf("=== 性能测试: %d 玩家, %d 次更新 ===\n", playerCount, updateCount)

	manager := NewQueueManager()

	config := &Config{
		RankType:       2,
		MinScoreLimit:  100,
		MaxQueryLimit:  500,
		ShowRankLimit:  300,
		MaxRankLimit:   10000, // 增大排行榜容量
		UpdateInterval: 0,
		IsRealtime:     true,
		QueueSize:      10000, // 增大队列容量
		WorkerCount:    4,     // 增加工作器
		BatchSize:      50,    // 增大批处理
	}

	err := manager.AddRankConfig(config)
	if err != nil {
		fmt.Printf("添加配置失败: %v\n", err)
		return
	}

	err = manager.Start()
	if err != nil {
		fmt.Printf("启动失败: %v\n", err)
		return
	}
	defer manager.Stop()

	ctx := context.Background()

	// 创建测试玩家
	players := make([]*Entry, playerCount)
	for i := 0; i < playerCount; i++ {
		players[i] = NewEntry(
			uint64(10000 + i),
			fmt.Sprintf("性能测试玩家%d", i),
			int32(1 + i%100),
			fmt.Sprintf("perf_avatar%d.jpg", i),
			int32(1000 + i*10),
		)
	}

	// 性能测试：批量更新
	fmt.Println("开始批量更新测试...")
	startTime := time.Now()

	for i := 0; i < updateCount; i++ {
		player := players[i%playerCount]
		// 随机增加繁荣度
		player.RankEntry.Score += int32(i % 100)

		err := manager.UpdateRank(ctx, 2, player)
		if err != nil {
			fmt.Printf("更新失败: %v\n", err)
		}
	}

	updateDuration := time.Since(startTime)

	// 等待队列处理完成
	fmt.Println("等待队列处理完成...")
	time.Sleep(3 * time.Second)

	// 性能测试：批量查询
	fmt.Println("开始批量查询测试...")
	queryStartTime := time.Now()

	queryCount := 1000
	for i := 0; i < queryCount; i++ {
		_, _, err := manager.GetRankList(ctx, 2, 1, 100)
		if err != nil {
			fmt.Printf("查询失败: %v\n", err)
		}
	}

	queryDuration := time.Since(queryStartTime)

	// 输出性能测试结果
	fmt.Printf("\n性能测试结果:\n")
	fmt.Printf("更新测试:\n")
	fmt.Printf("  总更新数: %d\n", updateCount)
	fmt.Printf("  耗时: %v\n", updateDuration)
	fmt.Printf("  每秒更新数: %.2f\n", float64(updateCount)/updateDuration.Seconds())

	fmt.Printf("查询测试:\n")
	fmt.Printf("  总查询数: %d\n", queryCount)
	fmt.Printf("  耗时: %v\n", queryDuration)
	fmt.Printf("  每秒查询数: %.2f\n", float64(queryCount)/queryDuration.Seconds())

	// 获取最终统计
	stats := manager.GetAllQueueStats()
	for rankType, stat := range stats {
		if rankType == 2 {
			fmt.Printf("队列统计:\n")
			fmt.Printf("  处理总数: %d\n", stat.ProcessedCount)
			fmt.Printf("  错误总数: %d\n", stat.ErrorCount)
			fmt.Printf("  成功率: %.2f%%\n",
				float64(stat.ProcessedCount-stat.ErrorCount)/float64(stat.ProcessedCount)*100)
		}
	}
}
