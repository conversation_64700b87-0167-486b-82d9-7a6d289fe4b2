package rank

import (
	"fmt"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/logger"
	"sort"
	"sync"
	"time"
)

// MemoryStore 内存排行榜存储
type MemoryStore struct {
	// 排行榜数据，按排行榜类型分组
	rankData map[int32]*MemoryRankData
	// 读写锁，每个排行榜一个锁
	rankMutex map[int32]*sync.RWMutex
	// 玩家信息管理器
	playerInfoManager *PlayerInfoManager
	// 是否已初始化
	initialized bool
}

// NewMemoryStore 创建新的内存存储
func NewMemoryStore() *MemoryStore {
	return &MemoryStore{
		rankData:          make(map[int32]*MemoryRankData),
		rankMutex:         make(map[int32]*sync.RWMutex),
		playerInfoManager: NewPlayerInfoManager(),
		initialized:       false,
	}
}

// InitializeRankTypes 初始化所有排行榜类型（提前创建所有rankData和rankMutex）
func (ms *MemoryStore) InitializeRankTypes(rankTypes []int32) {
	if ms.initialized {
		logger.Warn("MemoryStore already initialized")
		return
	}

	logger.Info("Initializing memory store with rank types", zap.Int("count", len(rankTypes)))

	// 提前创建所有排行榜的数据和锁
	for _, rankType := range rankTypes {
		ms.rankData[rankType] = NewMemoryRankData()
		ms.rankMutex[rankType] = &sync.RWMutex{}

		logger.Debug("Initialized rank type", zap.Int32("rankType", rankType))
	}

	ms.initialized = true
	logger.Info("Memory store initialization completed")
}

// getRankMutex 获取指定排行榜的锁
func (ms *MemoryStore) getRankMutex(rankType int32) *sync.RWMutex {
	// 直接从map中获取，因为已经预先初始化了所有排行榜类型
	mutex, exists := ms.rankMutex[rankType]
	if !exists {
		logger.Error("Rank type not initialized", zap.Int32("rankType", rankType))
		// 但这种情况不应该出现，为了避免panic创建一个新锁
		ms.rankMutex[rankType] = &sync.RWMutex{}
		return ms.rankMutex[rankType]
	}
	return mutex
}

// getRankData 获取指定排行榜的数据
func (ms *MemoryStore) getRankData(rankType int32) *MemoryRankData {
	// 直接从map中获取，因为已经预先初始化了所有排行榜类型
	data, exists := ms.rankData[rankType]
	if !exists {
		logger.Error("Rank type not initialized", zap.Int32("rankType", rankType))
		// 但这种情况不应该出现，为了避免panic创建一个新数据
		ms.rankData[rankType] = NewMemoryRankData()
		return ms.rankData[rankType]
	}
	return data
}

// UpdateEntry 更新排行榜条目
func (ms *MemoryStore) UpdateEntry(rankType int32, entry *Entry, config *Config) (int32, int32, error) {
	if entry == nil || entry.PlayerInfo == nil || entry.RankEntry == nil {
		return -1, -1, fmt.Errorf("invalid entry data")
	}

	// 先更新玩家基本外观信息，这个没想好是不是每次都需要更新，还是上榜之后才更新
	ms.playerInfoManager.UpdatePlayerInfo(entry.PlayerInfo)

	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.Lock()
	defer mutex.Unlock()

	playerID := entry.PlayerInfo.PlayerID
	score := entry.RankEntry.Score

	oldRank := int32(-1)
	if _, exists := data.PlayerMap[playerID]; exists {
		oldRank = ms.findPlayerRank(data, playerID)
	}

	// 检查分数是否达到动态最低分数限制
	if score < config.DynamicMinScoreLimit {
		// 如果玩家之前在榜上，需要移除，保持列表长度
		if _, exists := data.PlayerMap[playerID]; exists {
			ms.removeRankEntry(data, playerID)
			// 移除玩家后需要重新构建排序列表和更新动态限制
			ms.rebuildSortedList(data, config)
			return -1, oldRank, nil
		}
		return -1, -1, nil
	}

	// 更新或添加排行榜条目（只存储排行榜相关数据）
	rankEntry := &RankEntry{
		PlayerID:   playerID,
		Score:      score,
		UpdateTime: time.Now().Unix(),
	}
	data.PlayerMap[playerID] = rankEntry

	// 重新构建排序列表
	ms.rebuildSortedList(data, config)

	// 查找新排名
	newRank := ms.findPlayerRank(data, playerID)

	logger.Debug("Player rank updated",
		zap.Uint64("playerID", playerID),
		zap.Int32("rankType", rankType),
		zap.Int32("oldRank", oldRank),
		zap.Int32("newRank", newRank),
		zap.Int32("score", score))

	return newRank, oldRank, nil
}

// removeRankEntry 移除排行榜条目
func (ms *MemoryStore) removeRankEntry(data *MemoryRankData, playerID uint64) {
	delete(data.PlayerMap, playerID)

	// 从排序列表中移除
	for i, entry := range data.Entries {
		if entry.PlayerID == playerID {
			data.Entries = append(data.Entries[:i], data.Entries[i+1:]...)
			break
		}
	}

	data.TotalCount = int64(len(data.Entries))
	data.UpdateTime = time.Now().Unix()
}

// rebuildSortedList 重新构建排序列表
func (ms *MemoryStore) rebuildSortedList(data *MemoryRankData, config *Config) {
	// 将所有条目放入切片
	entries := make([]*RankEntry, 0, len(data.PlayerMap))
	for _, entry := range data.PlayerMap {
		entries = append(entries, entry)
	}

	// 按分数降序排序
	sort.Slice(entries, func(i, j int) bool {
		if entries[i].Score == entries[j].Score {
			// 分数相同时，按更新时间升序排序（先更新的排名靠前）
			if entries[i].UpdateTime == entries[j].UpdateTime {
				return entries[i].PlayerID < entries[j].PlayerID
			}
			return entries[i].UpdateTime < entries[j].UpdateTime
		}
		return entries[i].Score > entries[j].Score
	})

	// 限制排行榜大小
	if int32(len(entries)) > config.MaxRankLimit {
		// 移除超出限制的条目
		for i := config.MaxRankLimit; i < int32(len(entries)); i++ {
			delete(data.PlayerMap, entries[i].PlayerID)
		}
		entries = entries[:config.MaxRankLimit]
	}

	// 更新排名
	for i, entry := range entries {
		entry.Ranking = int32(i + 1)
	}

	// 动态更新MinScoreLimit
	ms.updateDynamicMinScoreLimit(config, entries)

	data.Entries = entries
	data.TotalCount = int64(len(entries))
	data.UpdateTime = time.Now().Unix()
}

// updateDynamicMinScoreLimit 动态更新最低分数限制
func (ms *MemoryStore) updateDynamicMinScoreLimit(config *Config, entries []*RankEntry) {
	// 如果排行榜已满员，将MinScoreLimit设置为最后一名的分数
	if int32(len(entries)) >= config.MaxRankLimit && len(entries) > 0 {
		lastEntry := entries[len(entries)-1]
		newMinLimit := lastEntry.Score

		// 动态限制应该始终等于最后一名的分数（当排行榜满员时）
		// 这样可以确保：
		// 1. 分数高于最后一名的玩家可以上榜
		// 2. 分数低于最后一名的玩家无法上榜
		// 3. 当榜上玩家分数降低时，动态限制也会相应降低
		if newMinLimit != config.DynamicMinScoreLimit {
			config.UpdateDynamicMinScoreLimit(newMinLimit)
		}
	} else if int32(len(entries)) < config.MaxRankLimit {
		// 如果排行榜未满员，动态限制应该回到静态限制
		// 这样可以让更多玩家有机会上榜
		if config.DynamicMinScoreLimit != config.MinScoreLimit {
			config.UpdateDynamicMinScoreLimit(config.MinScoreLimit)
		}
	}
}

// findPlayerRank 查找玩家排名
func (ms *MemoryStore) findPlayerRank(data *MemoryRankData, playerID uint64) int32 {
	for i, entry := range data.Entries {
		if entry.PlayerID == playerID {
			return int32(i + 1)
		}
	}
	return -1
}

// GetRankList 获取排行榜列表
func (ms *MemoryStore) GetRankList(rankType int32, start, count int32) ([]*Entry, int64, error) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.RLock()
	defer mutex.RUnlock()

	if start < 1 {
		start = 1
	}

	startIndex := start - 1
	endIndex := startIndex + count

	if startIndex >= int32(len(data.Entries)) {
		return []*Entry{}, data.TotalCount, nil
	}

	if endIndex > int32(len(data.Entries)) {
		endIndex = int32(len(data.Entries))
	}

	// 获取排行榜条目对应的玩家ID列表
	playerIDs := make([]uint64, endIndex-startIndex)
	for i := startIndex; i < endIndex; i++ {
		playerIDs[i-startIndex] = data.Entries[i].PlayerID
	}

	// 批量获取玩家信息
	playerInfoMap := ms.playerInfoManager.GetPlayerInfoBatch(playerIDs)

	// 组合完整的Entry
	result := make([]*Entry, endIndex-startIndex)
	for i := startIndex; i < endIndex; i++ {
		rankEntry := data.Entries[i]
		playerInfo := playerInfoMap[rankEntry.PlayerID]

		// 如果玩家信息不存在，创建一个默认的
		if playerInfo == nil {
			playerInfo = &PlayerInfo{
				PlayerID:   rankEntry.PlayerID,
				PlayerName: fmt.Sprintf("Player%d", rankEntry.PlayerID),
				Level:      1,
				Icon:       1,
				UpdateTime: rankEntry.UpdateTime,
			}
		}

		// 复制数据以避免外部修改
		result[i-startIndex] = &Entry{
			PlayerInfo: &PlayerInfo{
				PlayerID:   playerInfo.PlayerID,
				PlayerName: playerInfo.PlayerName,
				Level:      playerInfo.Level,
				Icon:       playerInfo.Icon,
				UpdateTime: playerInfo.UpdateTime,
			},
			RankEntry: &RankEntry{
				PlayerID:   rankEntry.PlayerID,
				Score:      rankEntry.Score,
				UpdateTime: rankEntry.UpdateTime,
				Ranking:    rankEntry.Ranking,
			},
		}
	}

	return result, data.TotalCount, nil
}

// GetPlayerRank 获取玩家排名
func (ms *MemoryStore) GetPlayerRank(rankType int32, playerID uint64) (int32, *Entry, error) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.RLock()
	defer mutex.RUnlock()

	rankEntry, exists := data.PlayerMap[playerID]
	if !exists {
		return -1, nil, nil
	}

	rank := ms.findPlayerRank(data, playerID)

	// 获取玩家基础信息
	playerInfo := ms.playerInfoManager.GetPlayerInfo(playerID)
	if playerInfo == nil {
		// 如果玩家信息不存在，创建一个默认的
		playerInfo = &PlayerInfo{
			PlayerID:   playerID,
			PlayerName: fmt.Sprintf("Player%d", playerID),
			Level:      1,
			Icon:       1,
			UpdateTime: rankEntry.UpdateTime,
		}
	}

	// 组合完整的Entry
	entry := &Entry{
		PlayerInfo: &PlayerInfo{
			PlayerID:   playerInfo.PlayerID,
			PlayerName: playerInfo.PlayerName,
			Level:      playerInfo.Level,
			Icon:       playerInfo.Icon,
			UpdateTime: playerInfo.UpdateTime,
		},
		RankEntry: &RankEntry{
			PlayerID:   rankEntry.PlayerID,
			Score:      rankEntry.Score,
			UpdateTime: rankEntry.UpdateTime,
			Ranking:    rankEntry.Ranking,
		},
	}

	return rank, entry, nil
}

// GetStats 获取统计信息
func (ms *MemoryStore) GetStats(rankType int32) (int64, int64) {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.RLock()
	defer mutex.RUnlock()

	return data.TotalCount, data.UpdateTime
}

// ClearRank 清空排行榜
func (ms *MemoryStore) ClearRank(rankType int32) error {
	mutex := ms.getRankMutex(rankType)
	data := ms.getRankData(rankType)

	mutex.Lock()
	defer mutex.Unlock()

	data.Entries = make([]*RankEntry, 0)
	data.PlayerMap = make(map[uint64]*RankEntry)
	data.TotalCount = 0
	data.UpdateTime = time.Now().Unix()

	logger.Info("Rank cleared", zap.Int32("rankType", rankType))
	return nil
}
