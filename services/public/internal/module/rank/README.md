# 排行榜队列系统

## 概述

这是一个基于内存的高性能排行榜系统，采用队列机制处理排行榜更新，每个排行榜使用独立的队列，避免并发问题，支持游戏逻辑服推送写入队列。

## 系统特性

### 核心特性
- **内存存储**: 所有排行榜数据存储在内存中，提供极高的读写性能
- **独立队列**: 每个排行榜使用独立的更新队列，避免不同排行榜之间的相互影响
- **异步处理**: 使用队列异步处理更新请求，避免并发冲突
- **批量处理**: 支持批量处理更新请求，提高处理效率
- **多工作器**: 每个队列支持多个工作器并行处理
- **配置化**: 支持灵活的配置，可针对不同排行榜设置不同的队列参数

### 性能特性
- **高并发**: 支持高并发的排行榜更新和查询
- **低延迟**: 内存存储提供毫秒级的查询响应
- **高吞吐**: 队列机制支持高吞吐量的更新操作
- **可扩展**: 支持动态调整队列大小和工作器数量

## 架构设计

### 组件架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   游戏逻辑服    │───▶│   队列管理器    │───▶│   内存存储      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   更新队列      │
                       │  (每个排行榜)   │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   工作器池      │
                       │  (批量处理)     │
                       └─────────────────┘
```

### 核心组件

1. **Manager**: 排行榜管理器，提供统一的接口
2. **QueueManager**: 队列管理器，管理所有排行榜的更新队列
3. **UpdateQueue**: 更新队列，处理单个排行榜的更新请求
4. **MemoryStore**: 内存存储，存储排行榜数据
5. **ConfigManager**: 配置管理器，管理排行榜配置

## 配置说明

### 排行榜配置
根据您提供的配置表，系统支持以下配置：

| 字段 | 说明 | 示例值 |
|------|------|--------|
| RankType | 排行榜类型 | 2(繁荣榜), 3(充值榜) |
| MinScoreLimit | 最低上榜分数 | 100, 10 |
| MaxQueryLimit | 查询人数上限 | 500 |
| ShowRankLimit | 榜上显示数量 | 300 |
| MaxRankLimit | 排行数量 | 1000 |
| UpdateInterval | 更新间隔(秒) | 5 |

### 队列配置
系统会根据排行榜类型自动设置队列配置：

| 排行榜类型 | 队列大小 | 工作器数量 | 批处理大小 |
|------------|----------|------------|------------|
| 繁荣榜(2) | 2000 | 3 | 20 |
| 充值榜(3) | 1500 | 2 | 15 |
| 其他 | 1000 | 2 | 10 |

## 使用方法

### 1. 初始化系统
```go
// 获取排行榜管理器
manager := GetRankManager()

// 初始化管理器
err := manager.Initialize()
if err != nil {
    log.Fatal("Failed to initialize rank manager:", err)
}
defer manager.Stop()
```

### 2. 更新排行榜
```go
// 创建排行榜条目
entry := &Entry{
    PlayerID:   1001,
    PlayerName: "Player1",
    Level:      10,
    Prosperity: 1500,
}

// 更新排行榜
err := manager.UpdateRankEntry(ctx, 2, entry) // 2为繁荣榜
if err != nil {
    log.Error("Failed to update rank:", err)
}
```

### 3. 查询排行榜
```go
// 获取排行榜列表
entries, totalCount, err := manager.GetRankList(ctx, 2, 1, 50)
if err != nil {
    log.Error("Failed to get rank list:", err)
    return
}

// 获取玩家排名
rank, entry, err := manager.GetPlayerRank(ctx, 2, 1001)
if err != nil {
    log.Error("Failed to get player rank:", err)
    return
}
```

### 4. 监控队列状态
```go
// 获取队列统计
stats, err := manager.GetQueueStats(2)
if err != nil {
    log.Error("Failed to get queue stats:", err)
    return
}

fmt.Printf("Queue Size: %d, Processed: %d, Errors: %d\n", 
    stats.QueueSize, stats.ProcessedCount, stats.ErrorCount)
```

## 性能优化

### 内存优化
- 使用对象池减少GC压力
- 合理设置队列大小避免内存溢出
- 定期清理过期数据

### 并发优化
- 每个排行榜使用独立的锁
- 读写分离，查询操作使用读锁
- 批量处理减少锁竞争

### 队列优化
- 根据业务特点调整队列大小
- 合理设置工作器数量
- 使用批处理提高吞吐量

## 监控指标

### 队列指标
- **QueueSize**: 当前队列长度
- **ProcessedCount**: 已处理请求数
- **ErrorCount**: 错误请求数
- **WorkerCount**: 工作器数量

### 性能指标
- **UpdatesPerSecond**: 每秒更新数
- **QueriesPerSecond**: 每秒查询数
- **AvgResponseTime**: 平均响应时间
- **ErrorRate**: 错误率

## 故障处理

### 队列满载
- 监控队列长度，及时调整队列大小
- 增加工作器数量提高处理能力
- 实现降级策略，丢弃低优先级请求

### 内存不足
- 限制排行榜最大条目数
- 定期清理过期数据
- 监控内存使用情况

### 数据一致性
- 使用原子操作保证数据一致性
- 实现数据校验机制
- 提供数据恢复功能

## 扩展功能

### 持久化
- 定期将内存数据持久化到Redis
- 启动时从Redis恢复数据
- 实现增量同步机制

### 分布式
- 支持多实例部署
- 实现数据分片
- 提供数据同步机制

### 实时通知
- 排名变化实时通知
- 支持WebSocket推送
- 实现事件驱动架构

## 测试验证

### 功能测试
```bash
go test -v ./services/public/internal/module/rank/
```

### 性能测试
```bash
go test -bench=. -benchmem ./services/public/internal/module/rank/
```

### 压力测试
```go
// 运行示例中的压力测试
BenchmarkRankSystem(1000, 10000)
```

## 注意事项

1. **内存限制**: 注意监控内存使用，避免OOM
2. **队列大小**: 根据业务量合理设置队列大小
3. **工作器数量**: 不要设置过多工作器，避免CPU竞争
4. **批处理大小**: 平衡延迟和吞吐量
5. **错误处理**: 实现完善的错误处理和恢复机制
