package rank

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/logger"
	"sync"
)

// Manager 排行榜管理器
type Manager struct {
	// 配置管理器
	configManager *ConfigManager
	// 队列管理器
	queueManager *QueueManager
	// 互斥锁
	mutex sync.RWMutex
	// 是否已初始化
	initialized bool
}

var (
	// 单例实例
	instance *Manager
	once     sync.Once
)

// GetRankManager 获取排行榜管理器实例
func GetRankManager() *Manager {
	once.Do(func() {
		instance = &Manager{
			configManager: NewConfigManager(),
			queueManager:  NewQueueManager(),
			initialized:   false,
		}
	})
	return instance
}

// Initialize 初始化排行榜管理器
func (m *Manager) Initialize() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.initialized {
		return fmt.Errorf("rank manager already initialized")
	}

	logger.Info("Initializing rank manager")

	// 加载配置
	err := m.configManager.LoadConfigs()
	if err != nil {
		return fmt.Errorf("failed to load configs: %w", err)
	}

	// 为每个排行榜添加配置到队列管理器
	configs := m.configManager.GetAllConfigs()
	for _, config := range configs {
		err := m.queueManager.AddRankConfig(config)
		if err != nil {
			return fmt.Errorf("failed to add rank config %d: %w", config.RankType, err)
		}
	}

	// 启动队列管理器
	err = m.queueManager.Start()
	if err != nil {
		return fmt.Errorf("failed to start queue manager: %w", err)
	}

	m.initialized = true
	logger.Info("Rank manager initialized successfully", zap.Int("rankCount", len(configs)))
	return nil
}

// Stop 停止排行榜管理器
func (m *Manager) Stop() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.initialized {
		return
	}

	logger.Info("Stopping rank manager")
	m.queueManager.Stop()
	m.initialized = false
	logger.Info("Rank manager stopped")
}

// UpdateRankEntry 更新排行榜条目
func (m *Manager) UpdateRankEntry(ctx context.Context, rankType int32, entry *Entry) error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return fmt.Errorf("rank manager not initialized")
	}

	config := m.configManager.GetConfig(rankType)
	if config == nil {
		return fmt.Errorf("rank type %d not found", rankType)
	}

	// 检查分数是否达到上榜要求
	if entry.RankEntry.Score < config.MinScoreLimit {
		logger.Debug("Player score below minimum limit",
			zap.Uint64("playerID", entry.PlayerInfo.PlayerID),
			zap.Int32("rankType", rankType),
			zap.Int32("score", entry.RankEntry.Score),
			zap.Int32("minLimit", config.MinScoreLimit))
		// 仍然需要处理，可能需要从榜上移除
	}
	// TODO 需要更新最低上榜分数

	return m.queueManager.UpdateRank(ctx, rankType, entry)
}

// GetRankList 获取排行榜列表
func (m *Manager) GetRankList(ctx context.Context, rankType int32, start, count int32) ([]*Entry, int64, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return nil, 0, fmt.Errorf("rank manager not initialized")
	}

	return m.queueManager.GetRankList(ctx, rankType, start, count)
}

// GetPlayerRank 获取玩家排名
func (m *Manager) GetPlayerRank(ctx context.Context, rankType int32, playerID uint64) (int32, *Entry, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return -1, nil, fmt.Errorf("rank manager not initialized")
	}

	return m.queueManager.GetPlayerRank(ctx, rankType, playerID)
}

// ClearRank 清空排行榜
func (m *Manager) ClearRank(ctx context.Context, rankType int32) error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return fmt.Errorf("rank manager not initialized")
	}

	return m.queueManager.ClearRank(ctx, rankType)
}

// GetQueueStats 获取队列统计信息
func (m *Manager) GetQueueStats(rankType int32) (QueueStats, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return QueueStats{}, fmt.Errorf("rank manager not initialized")
	}

	return m.queueManager.GetQueueStats(rankType)
}

// GetAllQueueStats 获取所有队列统计信息
func (m *Manager) GetAllQueueStats() map[int32]QueueStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return make(map[int32]QueueStats)
	}

	return m.queueManager.GetAllQueueStats()
}

// GetRankStats 获取排行榜统计信息
func (m *Manager) GetRankStats(rankType int32) (int64, int64, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return 0, 0, fmt.Errorf("rank manager not initialized")
	}

	return m.queueManager.GetRankStats(rankType)
}

// IsQueueFull 检查队列是否已满
func (m *Manager) IsQueueFull(rankType int32) bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return false
	}

	return m.queueManager.IsQueueFull(rankType)
}

// GetQueueLength 获取队列长度
func (m *Manager) GetQueueLength(rankType int32) int32 {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if !m.initialized {
		return 0
	}

	return m.queueManager.GetQueueLength(rankType)
}

// GetConfig 获取排行榜配置
func (m *Manager) GetConfig(rankType int32) *Config {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.configManager.GetConfig(rankType)
}

// GetAllConfigs 获取所有排行榜配置
func (m *Manager) GetAllConfigs() map[int32]*Config {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.configManager.GetAllConfigs()
}

// AddRankConfig 添加排行榜配置（仅在初始化前使用）
func (m *Manager) AddRankConfig(config *Config) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.initialized {
		return fmt.Errorf("cannot add config after initialization")
	}

	err := m.configManager.ValidateConfig(config)
	if err != nil {
		return fmt.Errorf("invalid config: %w", err)
	}

	m.configManager.AddConfig(config)
	return nil
}

// IsInitialized 检查是否已初始化
func (m *Manager) IsInitialized() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.initialized
}
