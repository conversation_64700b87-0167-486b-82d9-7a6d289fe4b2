package rank

import (
	"context"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
)

// 消息ID定义
const (
	UpdateRankRequestID    = 30100 // 更新排行榜数据请求
	GetRankListRequestID   = 30102 // 获取排行榜数据请求
	GetPlayerRankRequestID = 30104 // 获取玩家排名请求
	ClearRankRequestID     = 30106 // 清除排行榜请求
	GetQueueStatsRequestID = 30108 // 获取队列统计请求（新增）
)

// Request 请求结构
type Request struct {
	ProtocolId *uint32
	Payload    []byte
}

// Response 响应结构
type Response struct {
	ProtocolId *uint32
	Payload    []byte
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(req *Request, code pb.ResponseCode, message string) *Response {
	// 这里需要根据实际的错误响应结构来实现
	return &Response{
		ProtocolId: proto.Uint32(999), // 错误响应ID
		Payload:    []byte(message),
	}
}

// Register 注册排行榜消息处理器
func Register() {
	// 这里需要根据实际的handler注册方式来实现
	// 暂时只记录日志
	logger.Info("Rank handlers registered")
}

// HandleUpdateRank 处理更新排行榜请求
func HandleUpdateRank(ctx context.Context, payload []byte) (int32, error) {
	// 解析请求
	var updateReq pb.G2SUpdateRank
	err := proto.Unmarshal(payload, &updateReq)
	if err != nil {
		logger.Error("Failed to unmarshal update rank request", zap.Error(err))
		return -1, err
	}

	// 验证请求参数
	if updateReq.RankType == nil || updateReq.Entry == nil {
		logger.Error("Missing required fields in update rank request")
		return -1, err
	}

	rankType := int32(*updateReq.RankType)
	entry := NewEntryFromProto(updateReq.Entry)

	// 获取排行榜管理器
	manager := GetRankManager()
	if !manager.IsInitialized() {
		logger.Error("Rank manager not initialized")
		return -1, err
	}

	// 更新排行榜
	err = manager.UpdateRankEntry(ctx, rankType, entry)
	if err != nil {
		logger.Error("Failed to update rank entry",
			zap.Error(err),
			zap.Int32("rankType", rankType),
			zap.Uint64("playerID", entry.PlayerInfo.PlayerID))
		return -1, err
	}

	// 获取更新后的排名
	newRank, _, err := manager.GetPlayerRank(ctx, rankType, entry.PlayerInfo.PlayerID)
	if err != nil {
		logger.Warn("Failed to get player rank after update",
			zap.Error(err),
			zap.Int32("rankType", rankType),
			zap.Uint64("playerID", entry.PlayerInfo.PlayerID))
		newRank = -1 // 设置为未上榜
	}

	return newRank, nil
}

// HandleGetRankList 处理获取排行榜列表请求
func HandleGetRankList(ctx context.Context, payload []byte) ([]*Entry, int64, error) {
	// 解析请求
	var getRankListReq pb.C2SGetRankList
	err := proto.Unmarshal(payload, &getRankListReq)
	if err != nil {
		logger.Error("Failed to unmarshal get rank list request", zap.Error(err))
		return nil, 0, err
	}

	// 验证请求参数
	if getRankListReq.RankType == nil || getRankListReq.Start == nil || getRankListReq.Count == nil {
		logger.Error("Missing required fields in get rank list request")
		return nil, 0, err
	}

	rankType := int32(*getRankListReq.RankType)
	start := *getRankListReq.Start
	count := *getRankListReq.Count

	// 获取排行榜管理器
	manager := GetRankManager()
	if !manager.IsInitialized() {
		logger.Error("Rank manager not initialized")
		return nil, 0, err
	}

	// 获取排行榜列表
	entries, totalCount, err := manager.GetRankList(ctx, rankType, start, count)
	if err != nil {
		logger.Error("Failed to get rank list",
			zap.Error(err),
			zap.Int32("rankType", rankType),
			zap.Int32("start", start),
			zap.Int32("count", count))
		return nil, 0, err
	}

	return entries, totalCount, nil
}

// HandleGetPlayerRank 处理获取玩家排名请求
func HandleGetPlayerRank(ctx context.Context, payload []byte) (int32, *Entry, error) {
	// 解析请求
	var getPlayerRankReq pb.G2SGetPlayerRank
	err := proto.Unmarshal(payload, &getPlayerRankReq)
	if err != nil {
		logger.Error("Failed to unmarshal get player rank request", zap.Error(err))
		return -1, nil, err
	}

	// 验证请求参数
	if getPlayerRankReq.RankType == nil || getPlayerRankReq.PlayerId == nil {
		logger.Error("Missing required fields in get player rank request")
		return -1, nil, err
	}

	rankType := int32(*getPlayerRankReq.RankType)
	playerID := *getPlayerRankReq.PlayerId

	// 获取排行榜管理器
	manager := GetRankManager()
	if !manager.IsInitialized() {
		logger.Error("Rank manager not initialized")
		return -1, nil, err
	}

	// 获取玩家排名
	rank, entry, err := manager.GetPlayerRank(ctx, rankType, playerID)
	if err != nil {
		logger.Error("Failed to get player rank",
			zap.Error(err),
			zap.Int32("rankType", rankType),
			zap.Uint64("playerID", playerID))
		return -1, nil, err
	}

	return rank, entry, nil
}

// HandleClearRank 处理清除排行榜请求
func HandleClearRank(ctx context.Context, payload []byte) error {
	// 解析请求
	var clearRankReq pb.G2SClearRank
	err := proto.Unmarshal(payload, &clearRankReq)
	if err != nil {
		logger.Error("Failed to unmarshal clear rank request", zap.Error(err))
		return err
	}

	// 验证请求参数
	if clearRankReq.RankType == nil {
		logger.Error("Missing required fields in clear rank request")
		return err
	}

	rankType := int32(*clearRankReq.RankType)

	// 获取排行榜管理器
	manager := GetRankManager()
	if !manager.IsInitialized() {
		logger.Error("Rank manager not initialized")
		return err
	}

	// 清除排行榜
	err = manager.ClearRank(ctx, rankType)
	if err != nil {
		logger.Error("Failed to clear rank",
			zap.Error(err),
			zap.Int32("rankType", rankType))
		return err
	}

	return nil
}

// GetQueueStats 获取队列统计信息
func GetQueueStats() map[int32]QueueStats {
	// 获取排行榜管理器
	manager := GetRankManager()
	if !manager.IsInitialized() {
		logger.Error("Rank manager not initialized")
		return make(map[int32]QueueStats)
	}

	// 获取所有队列统计
	allStats := manager.GetAllQueueStats()

	// 记录统计信息到日志
	for rankType, stats := range allStats {
		logger.Info("Queue stats",
			zap.Int32("rankType", rankType),
			zap.Int32("queueSize", stats.QueueSize),
			zap.Int64("processedCount", stats.ProcessedCount),
			zap.Int64("errorCount", stats.ErrorCount),
			zap.Int32("workerCount", stats.WorkerCount))
	}

	return allStats
}
