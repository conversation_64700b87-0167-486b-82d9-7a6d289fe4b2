package rank

import (
	"fmt"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/logger"
)

// ConfigManager 配置管理器
type ConfigManager struct {
	configs map[int32]*Config
}

// NewConfigManager 创建新的配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		configs: make(map[int32]*Config),
	}
}

// LoadConfigs 从游戏配置加载排行榜配置
func (cm *ConfigManager) LoadConfigs() error {
	logger.Info("Loading rank configs from game config")

	// 从配置中加载排行榜信息
	for _, id := range game_config.RankConfig.GetIds() {
		config := game_config.RankConfig.Item(id)
		if config == nil {
			continue
		}

		// 创建排行榜配置
		rankConfig := &Config{
			RankType:       id,
			MinScoreLimit:  config.GetMinValueLimit(),
			MaxQueryLimit:  config.GetMaxQueryLimit(),
			ShowRankLimit:  config.GetShowRankLimit(),
			MaxRankLimit:   config.GetMaxRankLimit(),
			UpdateInterval: config.GetUpdateInterval() * 60,
			IsRealtime:     config.GetUpdateInterval() == 0,
		}

		// 设置默认值
		cm.setDefaultValues(rankConfig)

		// 根据排行榜类型设置缓存和队列配置
		cm.setTypeSpecificConfig(rankConfig)

		cm.configs[id] = rankConfig

		logger.Info("Loaded rank config",
			zap.Int32("rankType", id),
			zap.Int32("minScoreLimit", rankConfig.MinScoreLimit),
			zap.Int32("maxQueryLimit", rankConfig.MaxQueryLimit),
			zap.Int32("showRankLimit", rankConfig.ShowRankLimit),
			zap.Int32("maxRankLimit", rankConfig.MaxRankLimit),
			zap.Bool("isRealtime", rankConfig.IsRealtime),
			zap.Int32("queueSize", rankConfig.QueueSize),
			zap.Int32("workerCount", rankConfig.WorkerCount))
	}

	logger.Info("Rank configs loaded", zap.Int("count", len(cm.configs)))
	return nil
}

// setDefaultValues 设置默认值
func (cm *ConfigManager) setDefaultValues(config *Config) {
	// 如果没有设置MaxRankLimit，设置默认值
	if config.MaxRankLimit == 0 {
		config.MaxRankLimit = 2000
	}

	if config.MinScoreLimit == 0 {
		config.MinScoreLimit = 10
	}

	// 如果没有设置ShowRankLimit，使用MaxRankLimit的值
	if config.ShowRankLimit == 0 {
		config.ShowRankLimit = config.MaxRankLimit
	}

	// 如果没有设置MaxQueryLimit，设置默认值
	if config.MaxQueryLimit == 0 {
		config.MaxQueryLimit = 500 // 默认查询上限500
	}

}

// setTypeSpecificConfig 根据排行榜类型设置特定配置
func (cm *ConfigManager) setTypeSpecificConfig(config *Config) {
	switch config.RankType {
	case 2: // 繁荣榜
		// 实时排行榜配置
		config.QueueSize = 2000 // 较大的队列
		config.WorkerCount = 3  // 更多工作器
		config.BatchSize = 20   // 较大的批处理

	case 3: // 充值榜
		// 实时排行榜配置
		config.QueueSize = 1500 // 中等队列
		config.WorkerCount = 2  // 标准工作器
		config.BatchSize = 15   // 中等批处理

	default:
		// 默认配置
		config.QueueSize = 1000 // 标准队列
		config.WorkerCount = 2  // 标准工作器
		config.BatchSize = 10   // 标准批处理
	}

	// 如果是定时排行榜，调整配置
	if !config.IsRealtime {
		config.QueueSize = config.QueueSize / 2 // 减少队列大小
		config.WorkerCount = 1                  // 减少工作器
	}
}

// GetConfig 获取配置
func (cm *ConfigManager) GetConfig(rankType int32) *Config {
	return cm.configs[rankType]
}

// GetAllConfigs 获取所有配置
func (cm *ConfigManager) GetAllConfigs() map[int32]*Config {
	result := make(map[int32]*Config)
	for k, v := range cm.configs {
		result[k] = v
	}
	return result
}

// AddConfig 添加配置
func (cm *ConfigManager) AddConfig(config *Config) {
	cm.setDefaultValues(config)
	cm.setTypeSpecificConfig(config)
	cm.configs[config.RankType] = config
}

// RemoveConfig 移除配置
func (cm *ConfigManager) RemoveConfig(rankType int32) bool {
	if _, exists := cm.configs[rankType]; !exists {
		return false
	}

	delete(cm.configs, rankType)
	return true
}

// GetConfigCount 获取配置数量
func (cm *ConfigManager) GetConfigCount() int {
	return len(cm.configs)
}

// ValidateConfig 验证配置
func (cm *ConfigManager) ValidateConfig(config *Config) error {
	if config.RankType <= 0 {
		return fmt.Errorf("invalid rank type: %d", config.RankType)
	}

	if config.MaxRankLimit <= 0 {
		return fmt.Errorf("max rank limit must be positive: %d", config.MaxRankLimit)
	}

	if config.MaxQueryLimit <= 0 {
		return fmt.Errorf("max query limit must be positive: %d", config.MaxQueryLimit)
	}

	if config.ShowRankLimit <= 0 {
		return fmt.Errorf("show rank limit must be positive: %d", config.ShowRankLimit)
	}

	if config.QueueSize <= 0 {
		return fmt.Errorf("queue size must be positive: %d", config.QueueSize)
	}

	if config.WorkerCount <= 0 {
		return fmt.Errorf("worker count must be positive: %d", config.WorkerCount)
	}

	if config.BatchSize <= 0 {
		return fmt.Errorf("batch size must be positive: %d", config.BatchSize)
	}

	return nil
}
