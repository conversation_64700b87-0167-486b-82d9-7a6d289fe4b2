package main

import (
	"fmt"
	"time"

	"kairo_paradise_server/services/public/internal/module/rank"
)

// ExampleDynamicMinScoreLimit 展示动态最低分数限制的使用
func ExampleDynamicMinScoreLimit() {
	fmt.Println("=== 动态最低分数限制示例 ===")

	// 1. 创建内存存储
	fmt.Println("\n1. 创建内存存储")
	store := rank.NewMemoryStore()

	// 2. 创建排行榜配置
	fmt.Println("2. 创建排行榜配置")
	config := &rank.Config{
		RankType:       2,    // 繁荣榜
		MinScoreLimit:  100,  // 静态最低分数限制
		MaxQueryLimit:  500,  // 最多查询500条
		ShowRankLimit:  10,   // 显示前10名
		MaxRankLimit:   10,   // 最多10名（便于演示）
		UpdateInterval: 0,    // 实时更新
		IsRealtime:     true, // 实时排行榜
	}

	// 3. 初始化动态限制
	fmt.Println("3. 初始化动态限制")
	config.InitializeDynamicLimit()

	fmt.Printf("   静态最低分数限制: %d\n", config.StaticMinScoreLimit)
	fmt.Printf("   动态最低分数限制: %d\n", config.DynamicMinScoreLimit)
	fmt.Printf("   有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())

	// 4. 初始化排行榜类型
	fmt.Println("\n4. 初始化排行榜类型")
	store.InitializeRankTypes([]int32{2})

	// 5. 添加玩家数据（未满员阶段）
	fmt.Println("\n5. 添加玩家数据（未满员阶段）")
	players := []struct {
		playerID uint64
		name     string
		score    int32
	}{
		{1001, "Alice", 500},
		{1002, "Bob", 450},
		{1003, "Charlie", 400},
		{1004, "David", 350},
		{1005, "Eve", 300},
	}

	for _, player := range players {
		entry := rank.NewEntry(player.playerID, player.name, 10, 1, player.score)
		newRank, oldRank, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			fmt.Printf("   错误：更新玩家 %s 失败: %v\n", player.name, err)
			continue
		}

		fmt.Printf("   玩家 %s (分数: %d) 排名: %d (之前: %d)\n",
			player.name, player.score, newRank, oldRank)
		fmt.Printf("   当前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())
	}

	// 6. 继续添加玩家直到满员
	fmt.Println("\n6. 继续添加玩家直到满员")
	morePlayersToFill := []struct {
		playerID uint64
		name     string
		score    int32
	}{
		{1006, "Frank", 280},
		{1007, "Grace", 260},
		{1008, "Henry", 240},
		{1009, "Ivy", 220},
		{1010, "Jack", 200}, // 第10名，应该成为新的MinScoreLimit
	}

	for _, player := range morePlayersToFill {
		entry := rank.NewEntry(player.playerID, player.name, 10, 1, player.score)
		newRank, oldRank, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			fmt.Printf("   错误：更新玩家 %s 失败: %v\n", player.name, err)
			continue
		}

		fmt.Printf("   玩家 %s (分数: %d) 排名: %d (之前: %d)\n",
			player.name, player.score, newRank, oldRank)
		fmt.Printf("   当前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())
	}

	// 7. 尝试添加分数低于动态限制的玩家
	fmt.Println("\n7. 尝试添加分数低于动态限制的玩家")
	lowScorePlayers := []struct {
		playerID uint64
		name     string
		score    int32
	}{
		{1011, "Kate", 150}, // 低于动态限制，应该无法上榜
		{1012, "Leo", 180},  // 低于动态限制，应该无法上榜
	}

	for _, player := range lowScorePlayers {
		entry := rank.NewEntry(player.playerID, player.name, 10, 1, player.score)
		newRank, oldRank, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			fmt.Printf("   错误：更新玩家 %s 失败: %v\n", player.name, err)
			continue
		}

		if newRank == -1 {
			fmt.Printf("   玩家 %s (分数: %d) 分数过低，无法上榜\n", player.name, player.score)
		} else {
			fmt.Printf("   玩家 %s (分数: %d) 排名: %d (之前: %d)\n",
				player.name, player.score, newRank, oldRank)
		}
		fmt.Printf("   当前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())
	}

	// 8. 添加高分玩家，观察动态限制的变化
	fmt.Println("\n8. 添加高分玩家，观察动态限制的变化")
	highScorePlayers := []struct {
		playerID uint64
		name     string
		score    int32
	}{
		{1013, "Mike", 550},  // 高分，应该能上榜并推高动态限制
		{1014, "Nina", 480},  // 中等分数，应该能上榜
		{1015, "Oscar", 320}, // 较低分数，可能能上榜
	}

	for _, player := range highScorePlayers {
		entry := rank.NewEntry(player.playerID, player.name, 10, 1, player.score)
		newRank, oldRank, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			fmt.Printf("   错误：更新玩家 %s 失败: %v\n", player.name, err)
			continue
		}

		if newRank == -1 {
			fmt.Printf("   玩家 %s (分数: %d) 无法上榜\n", player.name, player.score)
		} else {
			fmt.Printf("   玩家 %s (分数: %d) 排名: %d (之前: %d)\n",
				player.name, player.score, newRank, oldRank)
		}
		fmt.Printf("   当前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())
	}

	// 9. 显示最终排行榜
	fmt.Println("\n9. 显示最终排行榜")
	rankList, totalCount, err := store.GetRankList(config.RankType, 1, 10)
	if err != nil {
		fmt.Printf("   错误：获取排行榜失败: %v\n", err)
		return
	}

	fmt.Printf("   排行榜总人数: %d\n", totalCount)
	fmt.Printf("   当前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())
	fmt.Println("   排行榜:")
	for i, entry := range rankList {
		if entry.PlayerInfo != nil && entry.RankEntry != nil {
			fmt.Printf("   %d. %s (ID: %d, 分数: %d)\n",
				i+1, entry.PlayerInfo.PlayerName, entry.PlayerInfo.PlayerID, entry.RankEntry.Score)
		}
	}

	// 10. 演示禁用动态限制
	fmt.Println("\n10. 演示禁用动态限制")
	fmt.Printf("   禁用前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())

	config.EnableDynamicLimit = false
	fmt.Printf("   禁用后有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())

	// 重新启用
	config.EnableDynamicLimit = true
	fmt.Printf("   重新启用后有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())

	fmt.Println("\n=== 动态最低分数限制示例完成 ===")
}

// ExampleConfigurationComparison 展示不同配置的对比
func ExampleConfigurationComparison() {
	fmt.Println("\n=== 配置对比示例 ===")

	// 静态配置
	staticConfig := &rank.Config{
		RankType:      1,
		MinScoreLimit: 100,
		MaxRankLimit:  5,
	}
	// 不调用InitializeDynamicLimit，保持静态

	// 动态配置
	dynamicConfig := &rank.Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  5,
	}
	dynamicConfig.InitializeDynamicLimit()

	fmt.Printf("静态配置 - 有效最低分数限制: %d\n", staticConfig.GetEffectiveMinScoreLimit())
	fmt.Printf("动态配置 - 有效最低分数限制: %d\n", dynamicConfig.GetEffectiveMinScoreLimit())

	// 模拟动态配置的变化
	dynamicConfig.UpdateDynamicMinScoreLimit(200)
	fmt.Printf("动态配置更新后 - 有效最低分数限制: %d\n", dynamicConfig.GetEffectiveMinScoreLimit())

	// 静态配置不会变化
	fmt.Printf("静态配置 - 有效最低分数限制: %d (保持不变)\n", staticConfig.GetEffectiveMinScoreLimit())
}

// ExamplePerformanceConsiderations 展示性能考虑
func ExamplePerformanceConsiderations() {
	fmt.Println("\n=== 性能考虑示例 ===")

	config := &rank.Config{
		RankType:      1,
		MinScoreLimit: 100,
		MaxRankLimit:  1000, // 较大的排行榜
	}
	config.InitializeDynamicLimit()

	store := rank.NewMemoryStore()
	store.InitializeRankTypes([]int32{1})

	// 测量添加大量玩家的时间
	start := time.Now()

	for i := 0; i < 1000; i++ {
		playerID := uint64(i + 1)
		score := int32(2000 - i) // 分数递减
		entry := rank.NewEntry(playerID, fmt.Sprintf("Player%d", playerID), 10, 1, score)

		_, _, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			fmt.Printf("错误：更新玩家 %d 失败: %v\n", playerID, err)
			continue
		}

		// 每100个玩家输出一次状态
		if (i+1)%100 == 0 {
			fmt.Printf("已添加 %d 个玩家，当前有效最低分数限制: %d\n",
				i+1, config.GetEffectiveMinScoreLimit())
		}
	}

	elapsed := time.Since(start)
	fmt.Printf("添加1000个玩家耗时: %v\n", elapsed)
	fmt.Printf("最终有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())
}

// main 主函数
func main() {
	ExampleDynamicMinScoreLimit()
	ExampleConfigurationComparison()
	ExamplePerformanceConsiderations()
}
