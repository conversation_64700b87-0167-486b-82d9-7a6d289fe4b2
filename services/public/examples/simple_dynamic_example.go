package main

import (
	"fmt"
	"kairo_paradise_server/services/public/internal/module/rank"
)

func main() {
	fmt.Println("=== 简化的动态最低分数限制示例 ===")

	// 创建内存存储
	store := rank.NewMemoryStore()

	// 创建排行榜配置
	config := &rank.Config{
		RankType:      1,
		MinScoreLimit: 100, // 静态最低分数限制
		MaxRankLimit:  5,   // 最多5名，便于演示
		IsRealtime:    true,
	}

	// 初始化动态限制
	config.InitializeDynamicLimit()

	fmt.Printf("初始状态:\n")
	fmt.Printf("  静态最低分数限制: %d\n", config.StaticMinScoreLimit)
	fmt.Printf("  动态最低分数限制: %d\n", config.DynamicMinScoreLimit)
	fmt.Printf("  有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())
	fmt.Printf("  是否启用动态限制: %t\n", config.EnableDynamicLimit)

	// 初始化排行榜类型
	store.InitializeRankTypes([]int32{1})

	// 添加玩家数据
	fmt.Println("\n添加玩家数据:")
	players := []struct {
		playerID uint64
		name     string
		score    int32
	}{
		{1001, "Alice", 500},
		{1002, "Bob", 450},
		{1003, "Charlie", 400},
		{1004, "David", 350},
		{1005, "Eve", 300},
		{1006, "Frank", 250},
		{1007, "Grace", 200}, // 第5名，应该成为新的MinScoreLimit
	}

	for _, player := range players {
		entry := rank.NewEntry(player.playerID, player.name, 10, 1, player.score)
		newRank, oldRank, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			fmt.Printf("  错误：更新玩家 %s 失败: %v\n", player.name, err)
			continue
		}

		fmt.Printf("  玩家 %s (分数: %d) 排名: %d (之前: %d), 有效限制: %d\n",
			player.name, player.score, newRank, oldRank, config.GetEffectiveMinScoreLimit())
	}

	// 尝试添加分数低于动态限制的玩家
	fmt.Println("\n尝试添加低分玩家:")
	lowScorePlayer := rank.NewEntry(1008, "Henry", 10, 1, 150)
	newRank, _, err := store.UpdateEntry(config.RankType, lowScorePlayer, config)
	if err != nil {
		fmt.Printf("  错误：%v\n", err)
	} else if newRank == -1 {
		fmt.Printf("  玩家 Henry (分数: 150) 分数过低，无法上榜\n")
	} else {
		fmt.Printf("  玩家 Henry (分数: 150) 排名: %d\n", newRank)
	}

	// 添加高分玩家
	fmt.Println("\n添加高分玩家:")
	highScorePlayer := rank.NewEntry(1009, "Ivy", 10, 1, 600)
	newRank, _, err = store.UpdateEntry(config.RankType, highScorePlayer, config)
	if err != nil {
		fmt.Printf("  错误：%v\n", err)
	} else {
		fmt.Printf("  玩家 Ivy (分数: 600) 排名: %d, 新的有效限制: %d\n",
			newRank, config.GetEffectiveMinScoreLimit())
	}

	// 显示最终排行榜
	fmt.Println("\n最终排行榜:")
	rankList, totalCount, err := store.GetRankList(config.RankType, 1, 10)
	if err != nil {
		fmt.Printf("  错误：获取排行榜失败: %v\n", err)
		return
	}

	fmt.Printf("  排行榜总人数: %d\n", totalCount)
	fmt.Printf("  当前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())
	for i, entry := range rankList {
		if entry.PlayerInfo != nil && entry.RankEntry != nil {
			fmt.Printf("  %d. %s (ID: %d, 分数: %d)\n",
				i+1, entry.PlayerInfo.PlayerName, entry.PlayerInfo.PlayerID, entry.RankEntry.Score)
		}
	}

	// 演示分数降低时动态限制也会降低
	fmt.Println("\n演示分数降低时动态限制的变化:")
	fmt.Printf("  当前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())

	// 更新所有玩家的分数，使其都降低
	fmt.Println("  所有玩家分数都降低:")
	lowerScorePlayers := []struct {
		playerID uint64
		name     string
		score    int32
	}{
		{1001, "Alice", 200},   // 从500降到200
		{1002, "Bob", 180},     // 从450降到180
		{1003, "Charlie", 160}, // 从400降到160
		{1004, "David", 140},   // 从350降到140
		{1006, "Frank", 120},   // 从250降到120
	}

	for _, player := range lowerScorePlayers {
		entry := rank.NewEntry(player.playerID, player.name, 10, 1, player.score)
		newRank, _, err := store.UpdateEntry(config.RankType, entry, config)
		if err != nil {
			fmt.Printf("    错误：更新玩家 %s 失败: %v\n", player.name, err)
			continue
		}
		fmt.Printf("    玩家 %s 分数降低到 %d, 排名: %d, 有效限制: %d\n",
			player.name, player.score, newRank, config.GetEffectiveMinScoreLimit())
	}

	// 现在一个分数为130的玩家应该能够上榜
	fmt.Println("  尝试添加分数为130的玩家:")
	mediumScorePlayer := rank.NewEntry(1010, "Jack", 10, 1, 130)
	newRank, _, err = store.UpdateEntry(config.RankType, mediumScorePlayer, config)
	if err != nil {
		fmt.Printf("    错误：%v\n", err)
	} else if newRank == -1 {
		fmt.Printf("    玩家 Jack (分数: 130) 无法上榜\n")
	} else {
		fmt.Printf("    玩家 Jack (分数: 130) 成功上榜，排名: %d, 新的有效限制: %d\n",
			newRank, config.GetEffectiveMinScoreLimit())
	}

	// 演示禁用动态限制
	fmt.Println("\n演示禁用动态限制:")
	fmt.Printf("  禁用前有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())

	config.EnableDynamicLimit = false
	fmt.Printf("  禁用后有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())

	// 重新启用
	config.EnableDynamicLimit = true
	fmt.Printf("  重新启用后有效最低分数限制: %d\n", config.GetEffectiveMinScoreLimit())

	fmt.Println("\n=== 示例完成 ===")
}
