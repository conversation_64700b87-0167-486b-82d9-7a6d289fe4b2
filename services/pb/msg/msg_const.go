
// --------------------------
// 工具生成
// --------------------------

package msg
import "kairo_paradise_server/services/pb"
type PCK int32
const (
	PCK_S2CCommResponse PCK = 10000
	PCK_C2SEnterGame PCK = 10001
	PCK_S2CEnterGame PCK = 10002
	PCK_S2CPlayerInfo PCK = 10003
	PCK_S2CLoginFinish PCK = 10004
	PCK_C2SPlayerModifyInfo PCK = 10005
	PCK_S2CPlayerModifyLevel PCK = 10006
	PCK_C2SAnnouncementList PCK = 10210
	PCK_S2CAnnouncementList PCK = 10211
	PCK_C2SAnnouncementRead PCK = 10212
	PCK_S2CAnnouncementRead PCK = 10213
	PCK_S2CAnnouncementNotify PCK = 10214
	PCK_C2SGMCreateAnnouncement PCK = 10215
	PCK_S2CGMCreateAnnouncement PCK = 10216
	PCK_S2CBackpackInit PCK = 10100
	PCK_S2CBackpackUpdateCoin PCK = 10101
	PCK_S2CBackpackAddItem PCK = 10102
	PCK_S2CBackpackSubItem PCK = 10103
	PCK_C2SBackpackUseItem PCK = 10104
	PCK_S2CBuildDataInit PCK = 10260
	PCK_C2SBuildDataSave PCK = 10261
	PCK_C2SClearBuild PCK = 10262
	PCK_C2SGM PCK = 10150
	PCK_C2SMailList PCK = 10160
	PCK_S2CMailList PCK = 10161
	PCK_C2SMailRead PCK = 10162
	PCK_S2CMailRead PCK = 10163
	PCK_C2SMailClaim PCK = 10164
	PCK_C2SMailDelete PCK = 10166
	PCK_S2CMailNotify PCK = 10167
	PCK_C2SGMSendMail PCK = 10168
	PCK_S2CGMSendMail PCK = 10169
	PCK_S2GEmpty PCK = 100
	PCK_G2CError PCK = 999
	PCK_S2GResponse PCK = 998
	PCK_C2GHeartbeat PCK = 1000
	PCK_G2CHeartbeat PCK = 1001
	PCK_G2SInit PCK = 1002
	PCK_G2SDisconnect PCK = 1003
	PCK_G2SUpdateRank PCK = 30100
	PCK_S2GUpdateRank PCK = 30101
	PCK_C2SGetRankList PCK = 30102
	PCK_S2CGetRankList PCK = 30103
	PCK_G2SGetPlayerRank PCK = 30104
	PCK_S2GGetPlayerRank PCK = 30105
	PCK_G2SClearRank PCK = 30106
)


func onInit() {
	S2CProcessor.Register(uint16(PCK_S2CCommResponse), &pb.S2CCommResponse{})
	C2SProcessor.Register(uint16(PCK_C2SEnterGame), &pb.C2SEnterGame{})
	S2CProcessor.Register(uint16(PCK_S2CEnterGame), &pb.S2CEnterGame{})
	S2CProcessor.Register(uint16(PCK_S2CPlayerInfo), &pb.S2CPlayerInfo{})
	S2CProcessor.Register(uint16(PCK_S2CLoginFinish), &pb.S2CLoginFinish{})
	C2SProcessor.Register(uint16(PCK_C2SPlayerModifyInfo), &pb.C2SPlayerModifyInfo{})
	S2CProcessor.Register(uint16(PCK_S2CPlayerModifyLevel), &pb.S2CPlayerModifyLevel{})
	C2SProcessor.Register(uint16(PCK_C2SAnnouncementList), &pb.C2SAnnouncementList{})
	S2CProcessor.Register(uint16(PCK_S2CAnnouncementList), &pb.S2CAnnouncementList{})
	C2SProcessor.Register(uint16(PCK_C2SAnnouncementRead), &pb.C2SAnnouncementRead{})
	S2CProcessor.Register(uint16(PCK_S2CAnnouncementRead), &pb.S2CAnnouncementRead{})
	S2CProcessor.Register(uint16(PCK_S2CAnnouncementNotify), &pb.S2CAnnouncementNotify{})
	C2SProcessor.Register(uint16(PCK_C2SGMCreateAnnouncement), &pb.C2SGMCreateAnnouncement{})
	S2CProcessor.Register(uint16(PCK_S2CGMCreateAnnouncement), &pb.S2CGMCreateAnnouncement{})
	S2CProcessor.Register(uint16(PCK_S2CBackpackInit), &pb.S2CBackpackInit{})
	S2CProcessor.Register(uint16(PCK_S2CBackpackUpdateCoin), &pb.S2CBackpackUpdateCoin{})
	S2CProcessor.Register(uint16(PCK_S2CBackpackAddItem), &pb.S2CBackpackAddItem{})
	S2CProcessor.Register(uint16(PCK_S2CBackpackSubItem), &pb.S2CBackpackSubItem{})
	C2SProcessor.Register(uint16(PCK_C2SBackpackUseItem), &pb.C2SBackpackUseItem{})
	S2CProcessor.Register(uint16(PCK_S2CBuildDataInit), &pb.S2CBuildDataInit{})
	C2SProcessor.Register(uint16(PCK_C2SBuildDataSave), &pb.C2SBuildDataSave{})
	C2SProcessor.Register(uint16(PCK_C2SClearBuild), &pb.C2SClearBuild{})
	C2SProcessor.Register(uint16(PCK_C2SGM), &pb.C2SGM{})
	C2SProcessor.Register(uint16(PCK_C2SMailList), &pb.C2SMailList{})
	S2CProcessor.Register(uint16(PCK_S2CMailList), &pb.S2CMailList{})
	C2SProcessor.Register(uint16(PCK_C2SMailRead), &pb.C2SMailRead{})
	S2CProcessor.Register(uint16(PCK_S2CMailRead), &pb.S2CMailRead{})
	C2SProcessor.Register(uint16(PCK_C2SMailClaim), &pb.C2SMailClaim{})
	C2SProcessor.Register(uint16(PCK_C2SMailDelete), &pb.C2SMailDelete{})
	S2CProcessor.Register(uint16(PCK_S2CMailNotify), &pb.S2CMailNotify{})
	C2SProcessor.Register(uint16(PCK_C2SGMSendMail), &pb.C2SGMSendMail{})
	S2CProcessor.Register(uint16(PCK_S2CGMSendMail), &pb.S2CGMSendMail{})
	S2CProcessor.Register(uint16(PCK_S2GEmpty), &pb.S2GEmpty{})
	S2CProcessor.Register(uint16(PCK_G2CError), &pb.G2CError{})
	S2CProcessor.Register(uint16(PCK_S2GResponse), &pb.S2GResponse{})
	C2SProcessor.Register(uint16(PCK_C2GHeartbeat), &pb.C2GHeartbeat{})
	S2CProcessor.Register(uint16(PCK_G2CHeartbeat), &pb.G2CHeartbeat{})
	C2SProcessor.Register(uint16(PCK_G2SInit), &pb.G2SInit{})
	C2SProcessor.Register(uint16(PCK_G2SDisconnect), &pb.G2SDisconnect{})
	C2SProcessor.Register(uint16(PCK_G2SUpdateRank), &pb.G2SUpdateRank{})
	S2CProcessor.Register(uint16(PCK_S2GUpdateRank), &pb.S2GUpdateRank{})
	C2SProcessor.Register(uint16(PCK_C2SGetRankList), &pb.C2SGetRankList{})
	S2CProcessor.Register(uint16(PCK_S2CGetRankList), &pb.S2CGetRankList{})
	C2SProcessor.Register(uint16(PCK_G2SGetPlayerRank), &pb.G2SGetPlayerRank{})
	S2CProcessor.Register(uint16(PCK_S2GGetPlayerRank), &pb.S2GGetPlayerRank{})
	C2SProcessor.Register(uint16(PCK_G2SClearRank), &pb.G2SClearRank{})
}