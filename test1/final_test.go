package test1

import (
	"fmt"
	"kairo_paradise_server/services/public/internal/module/rank"
	"testing"
	"time"
)

// 简化的日志接口，避免依赖外部包
type simpleLogger struct{}

func (l *simpleLogger) Debug(msg string, fields ...interface{}) {
	fmt.Printf("[DEBUG] %s\n", msg)
}

func (l *simpleLogger) Info(msg string, fields ...interface{}) {
	fmt.Printf("[INFO] %s\n", msg)
}

func (l *simpleLogger) Warn(msg string, fields ...interface{}) {
	fmt.Printf("[WARN] %s\n", msg)
}

func (l *simpleLogger) Error(msg string, fields ...interface{}) {
	fmt.Printf("[ERROR] %s\n", msg)
}

// TestPlayerInfoManagerStandalone 测试玩家信息管理器（独立版本）
func TestPlayerInfoManagerStandalone(t *testing.T) {
	pim := rank.NewPlayerInfoManager()

	// 测试添加玩家信息
	playerInfo := &rank.PlayerInfo{
		PlayerID:   1001,
		PlayerName: "张三",
		Level:      10,
		Avatar:     "avatar1.jpg",
		UpdateTime: time.Now().Unix(),
	}

	pim.UpdatePlayerInfo(playerInfo)

	// 测试获取玩家信息
	retrievedInfo := pim.GetPlayerInfo(1001)
	if retrievedInfo == nil {
		t.Fatal("Failed to retrieve player info")
	}

	if retrievedInfo.PlayerName != "张三" {
		t.Errorf("Expected player name '张三', got '%s'", retrievedInfo.PlayerName)
	}

	if retrievedInfo.Level != 10 {
		t.Errorf("Expected level 10, got %d", retrievedInfo.Level)
	}

	// 测试更新玩家等级
	success := pim.UpdatePlayerLevel(1001, 15)
	if !success {
		t.Error("Failed to update player level")
	}

	updatedInfo := pim.GetPlayerInfo(1001)
	if updatedInfo.Level != 15 {
		t.Errorf("Expected updated level 15, got %d", updatedInfo.Level)
	}

	// 测试统计信息
	totalPlayers, updateCount := pim.GetStats()
	if totalPlayers != 1 {
		t.Errorf("Expected 1 total player, got %d", totalPlayers)
	}

	if updateCount < 2 { // 至少有添加和更新两次操作
		t.Errorf("Expected at least 2 updates, got %d", updateCount)
	}

	fmt.Printf("✓ PlayerInfoManager test passed\n")
}

// TestNewArchitectureBasic 测试新架构的基本功能
func TestNewArchitectureBasic(t *testing.T) {
	// 创建内存存储
	store := rank.NewSimpleMemoryStore()

	// 创建配置
	config := &rank.Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	// 创建测试条目
	entry := rank.NewEntry(1001, "张三", 10, "avatar1.jpg", 1500)

	// 测试更新条目
	newRank, oldRank, err := store.UpdateEntry(2, entry, config)
	if err != nil {
		t.Fatalf("Failed to update entry: %v", err)
	}

	if newRank != 1 {
		t.Errorf("Expected rank 1, got %d", newRank)
	}

	if oldRank != -1 {
		t.Errorf("Expected old rank -1, got %d", oldRank)
	}

	// 验证玩家信息已存储到公共存储
	playerInfo := store.GetPlayerInfoManager().GetPlayerInfo(1001)
	if playerInfo == nil {
		t.Fatal("Player info not found in shared storage")
	}

	if playerInfo.PlayerName != "张三" {
		t.Errorf("Expected player name '张三', got '%s'", playerInfo.PlayerName)
	}

	// 测试获取排行榜列表
	entries, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if len(entries) != 1 {
		t.Errorf("Expected 1 entry, got %d", len(entries))
	}

	if totalCount != 1 {
		t.Errorf("Expected total count 1, got %d", totalCount)
	}

	// 验证返回的条目包含完整信息
	if entries[0].PlayerInfo == nil {
		t.Fatal("Player info is nil in returned entry")
	}

	if entries[0].RankEntry == nil {
		t.Fatal("Rank entry is nil in returned entry")
	}

	if entries[0].PlayerInfo.PlayerName != "张三" {
		t.Errorf("Expected player name '张三', got '%s'", entries[0].PlayerInfo.PlayerName)
	}

	if entries[0].RankEntry.Score != 1500 {
		t.Errorf("Expected score 1500, got %d", entries[0].RankEntry.Score)
	}

	fmt.Printf("✓ New architecture basic test passed\n")
}

// TestPlayerInfoSharing 测试玩家信息共享
func TestPlayerInfoSharing1(t *testing.T) {
	store := rank.NewSimpleMemoryStore()

	// 创建两个不同排行榜的配置
	prosperityConfig := &rank.Config{
		RankType:      2, // 繁荣榜
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	rechargeConfig := &rank.Config{
		RankType:      3, // 充值榜
		MinScoreLimit: 10,
		MaxRankLimit:  1000,
	}

	// 同一个玩家在不同排行榜中
	playerID := uint64(1001)
	playerName := "张三"
	level := int32(10)
	avatar := "avatar1.jpg"

	// 在繁荣榜中添加玩家
	prosperityEntry := rank.NewEntry(playerID, playerName, level, avatar, 1500)
	_, _, err := store.UpdateEntry(2, prosperityEntry, prosperityConfig)
	if err != nil {
		t.Fatalf("Failed to update prosperity entry: %v", err)
	}

	// 在充值榜中添加同一个玩家（不同分数）
	rechargeEntry := rank.NewEntry(playerID, playerName, level, avatar, 500)
	_, _, err = store.UpdateEntry(3, rechargeEntry, rechargeConfig)
	if err != nil {
		t.Fatalf("Failed to update recharge entry: %v", err)
	}

	// 验证玩家信息只存储一份
	pim := store.GetPlayerInfoManager()
	playerCount := pim.GetPlayerCount()
	if playerCount != 1 {
		t.Errorf("Expected 1 player in shared storage, got %d", playerCount)
	}

	// 获取两个排行榜的数据
	prosperityList, _, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get prosperity rank list: %v", err)
	}

	rechargeList, _, err := store.GetRankList(3, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get recharge rank list: %v", err)
	}

	// 验证两个排行榜都有该玩家，但分数不同
	if len(prosperityList) != 1 || len(rechargeList) != 1 {
		t.Fatal("Expected 1 entry in each rank list")
	}

	// 验证玩家基础信息相同
	if prosperityList[0].PlayerInfo.PlayerName != rechargeList[0].PlayerInfo.PlayerName {
		t.Error("Player names should be the same across different ranks")
	}

	// 验证分数不同
	if prosperityList[0].RankEntry.Score == rechargeList[0].RankEntry.Score {
		t.Error("Scores should be different across different ranks")
	}

	if prosperityList[0].RankEntry.Score != 1500 {
		t.Errorf("Expected prosperity score 1500, got %d", prosperityList[0].RankEntry.Score)
	}

	if rechargeList[0].RankEntry.Score != 500 {
		t.Errorf("Expected recharge score 500, got %d", rechargeList[0].RankEntry.Score)
	}

	// 更新玩家基础信息（如等级）
	success := pim.UpdatePlayerLevel(playerID, 25)
	if !success {
		t.Error("Failed to update player level")
	}

	// 验证两个排行榜中的玩家等级都更新了
	updatedProsperityList, _, _ := store.GetRankList(2, 1, 10)
	updatedRechargeList, _, _ := store.GetRankList(3, 1, 10)

	if updatedProsperityList[0].PlayerInfo.Level != 25 {
		t.Errorf("Expected updated level 25 in prosperity rank, got %d",
			updatedProsperityList[0].PlayerInfo.Level)
	}

	if updatedRechargeList[0].PlayerInfo.Level != 25 {
		t.Errorf("Expected updated level 25 in recharge rank, got %d",
			updatedRechargeList[0].PlayerInfo.Level)
	}

	fmt.Printf("✓ Player info sharing test passed\n")
}

// TestMultiplePlayersOrdering 测试多个玩家的排序
func TestMultiplePlayersOrdering(t *testing.T) {
	store := rank.NewMemoryStore()
	config := &rank.Config{
		RankType:      2,
		MinScoreLimit: 100,
		MaxRankLimit:  1000,
	}

	// 添加多个玩家
	players := []*rank.Entry{
		rank.NewEntry(1001, "张三", 10, "avatar1.jpg", 1500),
		rank.NewEntry(1002, "李四", 15, "avatar2.jpg", 2000),
		rank.NewEntry(1003, "王五", 12, "avatar3.jpg", 1800),
		rank.NewEntry(1004, "赵六", 8, "avatar4.jpg", 1200),
		rank.NewEntry(1005, "钱七", 20, "avatar5.jpg", 2500),
	}

	for _, player := range players {
		_, _, err := store.UpdateEntry(2, player, config)
		if err != nil {
			t.Fatalf("Failed to update entry for player %d: %v", player.PlayerInfo.PlayerID, err)
		}
	}

	// 检查排序是否正确（按分数降序）
	rankList, totalCount, err := store.GetRankList(2, 1, 10)
	if err != nil {
		t.Fatalf("Failed to get rank list: %v", err)
	}

	if totalCount != 5 {
		t.Errorf("Expected total count 5, got %d", totalCount)
	}

	if len(rankList) != 5 {
		t.Errorf("Expected 5 entries, got %d", len(rankList))
	}

	// 验证排序：钱七(2500) > 李四(2000) > 王五(1800) > 张三(1500) > 赵六(1200)
	expectedOrder := []uint64{1005, 1002, 1003, 1001, 1004}
	expectedScores := []int32{2500, 2000, 1800, 1500, 1200}
	expectedNames := []string{"钱七", "李四", "王五", "张三", "赵六"}

	for i, entry := range rankList {
		if entry.PlayerInfo.PlayerID != expectedOrder[i] {
			t.Errorf("Expected player %d at rank %d, got player %d",
				expectedOrder[i], i+1, entry.PlayerInfo.PlayerID)
		}

		if entry.RankEntry.Score != expectedScores[i] {
			t.Errorf("Expected score %d for player %d, got %d",
				expectedScores[i], entry.PlayerInfo.PlayerID, entry.RankEntry.Score)
		}

		if entry.PlayerInfo.PlayerName != expectedNames[i] {
			t.Errorf("Expected name '%s' for rank %d, got '%s'",
				expectedNames[i], i+1, entry.PlayerInfo.PlayerName)
		}

		if entry.RankEntry.Ranking != int32(i+1) {
			t.Errorf("Expected ranking %d for player %d, got %d",
				i+1, entry.PlayerInfo.PlayerID, entry.RankEntry.Ranking)
		}
	}

	fmt.Printf("✓ Multiple players ordering test passed\n")
}

// TestAllNewArchitecture 运行所有新架构测试
func TestAllNewArchitecture(t *testing.T) {
	fmt.Println("=== 新架构排行榜系统测试 ===")

	t.Run("PlayerInfoManager", TestPlayerInfoManagerStandalone)
	t.Run("BasicFunctionality", TestNewArchitectureBasic)
	t.Run("PlayerInfoSharing", TestPlayerInfoSharing)
	t.Run("MultiplePlayersOrdering", TestMultiplePlayersOrdering)

	fmt.Println("=== 所有测试通过！ ===")
}
